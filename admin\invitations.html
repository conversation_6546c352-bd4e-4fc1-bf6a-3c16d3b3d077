<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الدعوات | دعوات إلكترونية</title>
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/responsive.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Tajawal is a good Arabic font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/admin.css">
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <div class="admin-logo">
                    <div class="text-logo">دعوات إلكترونية</div>
                </div>
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="صورة المستخدم">
                </div>
                <div class="user-info">
                    <h3>أحمد محمد</h3>
                    <p>مدير النظام</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="index.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>المستخدمين</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="invitations.html">
                            <i class="fas fa-envelope-open-text"></i>
                            <span>الدعوات</span>
                        </a>
                    </li>
                    <li>
                        <a href="templates.html">
                            <i class="fas fa-paint-brush"></i>
                            <span>قوالب التصميم</span>
                        </a>
                    </li>
                    <li>
                        <a href="packages.html">
                            <i class="fas fa-box"></i>
                            <span>الباقات</span>
                        </a>
                    </li>
                    <li>
                        <a href="payments.html">
                            <i class="fas fa-credit-card"></i>
                            <span>المدفوعات</span>
                        </a>
                    </li>
                    <li>
                        <a href="reports.html">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <a href="login.html" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="بحث...">
                </div>

                <div class="header-actions">
                    <div class="header-notification">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="header-message">
                        <i class="fas fa-envelope"></i>
                        <span class="message-badge">5</span>
                    </div>
                    <div class="header-profile">
                        <img src="images/admin-avatar.png" alt="صورة المستخدم">
                    </div>
                </div>
            </header>

            <div class="admin-content">
                <div class="page-header">
                    <h1>إدارة الدعوات</h1>
                    <a href="../create-invitation.html" class="btn btn-primary add-new-btn">
                        <i class="fas fa-plus"></i>
                        إضافة دعوة جديدة
                    </a>
                </div>

                <!-- Invitations Filters -->
                <div class="filters-container">
                    <div class="filters-wrapper">
                        <div class="filter-group">
                            <label for="status-filter">الحالة</label>
                            <select id="status-filter">
                                <option value="all">الكل</option>
                                <option value="active">نشطة</option>
                                <option value="completed">مكتملة</option>
                                <option value="cancelled">ملغية</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="type-filter">نوع المناسبة</label>
                            <select id="type-filter">
                                <option value="all">الكل</option>
                                <option value="wedding">زفاف</option>
                                <option value="birthday">عيد ميلاد</option>
                                <option value="graduation">تخرج</option>
                                <option value="conference">مؤتمر</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="date-filter">تاريخ المناسبة</label>
                            <select id="date-filter">
                                <option value="all">الكل</option>
                                <option value="today">اليوم</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                                <option value="past">مناسبات سابقة</option>
                                <option value="future">مناسبات قادمة</option>
                            </select>
                        </div>
                    </div>

                    <div class="search-wrapper">
                        <input type="text" placeholder="بحث عن دعوة...">
                        <button class="btn btn-primary">بحث</button>
                    </div>
                </div>

                <!-- Invitations Table -->
                <div class="data-table invitations-table">
                    <div class="table-content">
                        <table>
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="select-all">
                                    </th>
                                    <th>عنوان الدعوة</th>
                                    <th>المستخدم</th>
                                    <th>نوع المناسبة</th>
                                    <th>تاريخ المناسبة</th>
                                    <th>عدد المدعوين</th>
                                    <th>نسبة التأكيد</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="invitation-checkbox">
                                    </td>
                                    <td>حفل زفاف نورة وأحمد</td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user2.png" alt="صورة المستخدم">
                                            <span>نورة الشمري</span>
                                        </div>
                                    </td>
                                    <td>زفاف</td>
                                    <td>15/05/2025</td>
                                    <td>250</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress" style="width: 85%;">85%</div>
                                        </div>
                                    </td>
                                    <td><span class="status active">نشطة</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض" data-id="حفل-زفاف-أحمد-ونورة">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل" data-id="حفل-زفاف-أحمد-ونورة">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف" data-id="حفل-زفاف-أحمد-ونورة">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="invitation-checkbox">
                                    </td>
                                    <td>مؤتمر التقنية السعودي</td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user3.png" alt="صورة المستخدم">
                                            <span>محمد السالم</span>
                                        </div>
                                    </td>
                                    <td>مؤتمر</td>
                                    <td>20/05/2025</td>
                                    <td>500</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress" style="width: 60%;">60%</div>
                                        </div>
                                    </td>
                                    <td><span class="status active">نشطة</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض" data-id="مؤتمر-التقنية-السعودي">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل" data-id="مؤتمر-التقنية-السعودي">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف" data-id="مؤتمر-التقنية-السعودي">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="invitation-checkbox">
                                    </td>
                                    <td>حفل تخرج كلية الهندسة</td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user5.png" alt="صورة المستخدم">
                                            <span>عبدالله الدوسري</span>
                                        </div>
                                    </td>
                                    <td>تخرج</td>
                                    <td>25/05/2025</td>
                                    <td>300</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress" style="width: 45%;">45%</div>
                                        </div>
                                    </td>
                                    <td><span class="status active">نشطة</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="invitation-checkbox">
                                    </td>
                                    <td>عيد ميلاد سارة</td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user4.png" alt="صورة المستخدم">
                                            <span>فاطمة الزهراني</span>
                                        </div>
                                    </td>
                                    <td>عيد ميلاد</td>
                                    <td>10/05/2025</td>
                                    <td>50</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress" style="width: 90%;">90%</div>
                                        </div>
                                    </td>
                                    <td><span class="status active">نشطة</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="invitation-checkbox">
                                    </td>
                                    <td>افتتاح معرض الفنون</td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user1.png" alt="صورة المستخدم">
                                            <span>سعد العتيبي</span>
                                        </div>
                                    </td>
                                    <td>معرض</td>
                                    <td>18/05/2025</td>
                                    <td>150</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress" style="width: 70%;">70%</div>
                                        </div>
                                    </td>
                                    <td><span class="status active">نشطة</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="invitation-checkbox">
                                    </td>
                                    <td>حفل عشاء شركة التقنية</td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user7.png" alt="صورة المستخدم">
                                            <span>خالد المالكي</span>
                                        </div>
                                    </td>
                                    <td>أخرى</td>
                                    <td>05/04/2025</td>
                                    <td>100</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress" style="width: 100%;">100%</div>
                                        </div>
                                    </td>
                                    <td><span class="status completed">مكتملة</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="invitation-checkbox">
                                    </td>
                                    <td>حفل استقبال المولود</td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user8.png" alt="صورة المستخدم">
                                            <span>منى الحربي</span>
                                        </div>
                                    </td>
                                    <td>أخرى</td>
                                    <td>12/03/2025</td>
                                    <td>80</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress" style="width: 0%;">0%</div>
                                        </div>
                                    </td>
                                    <td><span class="status cancelled">ملغية</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination">
                    <div class="pagination-info">
                        <p>عرض 1-7 من 42 دعوة</p>
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn">4</button>
                        <button class="pagination-btn">5</button>
                        <button class="pagination-btn">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                    <div class="pagination-options">
                        <label for="rows-per-page">عرض</label>
                        <select id="rows-per-page">
                            <option value="7">7</option>
                            <option value="14">14</option>
                            <option value="21">21</option>
                            <option value="28">28</option>
                        </select>
                        <span>لكل صفحة</span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../js/main.js"></script>
    <script src="../js/invitations-manager.js"></script>
    <style>
        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background-color: white;
            border-radius: 10px;
            width: 80%;
            max-width: 900px;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            transform: translateY(20px);
            transition: transform 0.3s;
        }

        .modal.show .modal-content {
            transform: translateY(0);
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
            color: #333;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #666;
            transition: color 0.3s;
        }

        .close-modal:hover {
            color: #333;
        }

        .modal-body {
            padding: 20px;
            overflow-y: auto;
            flex-grow: 1;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        /* Invitation Details Styles */
        .invitation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .invitation-header h3 {
            margin: 0;
            font-size: 1.3rem;
            color: #333;
        }

        .invitation-info {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .info-group label {
            font-weight: 600;
            color: #666;
            font-size: 0.9rem;
        }

        .info-group span {
            color: #333;
        }

        .invitation-preview {
            margin-top: 30px;
        }

        .invitation-preview h4 {
            margin-bottom: 15px;
            color: #333;
        }

        .preview-frame {
            width: 100%;
            height: 400px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            overflow: hidden;
        }

        .preview-frame iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle sidebar
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            const adminContainer = document.querySelector('.admin-container');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    adminContainer.classList.toggle('sidebar-collapsed');
                });
            }

            // Select all checkboxes
            const selectAllCheckbox = document.getElementById('select-all');
            const invitationCheckboxes = document.querySelectorAll('.invitation-checkbox');

            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    invitationCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                });
            }

            // Delete invitation functionality
            const deleteButtons = document.querySelectorAll('.delete-btn');

            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (confirm('هل أنت متأكد من حذف هذه الدعوة؟')) {
                        // Delete invitation logic would go here
                        const row = this.closest('tr');
                        row.remove();
                    }
                });
            });
        });
    </script>
    <style>
        /* Additional styles for invitations page */
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress {
            height: 100%;
            background-color: #4cd964;
            border-radius: 4px;
            text-align: center;
            font-size: 0.7rem;
            color: white;
            line-height: 8px;
        }

        .status.completed {
            background-color: rgba(76, 217, 100, 0.1);
            color: var(--admin-success);
        }

        .status.cancelled {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--admin-danger);
        }

        /* Enhanced action buttons */
        .actions {
            display: flex;
            gap: 5px;
            justify-content: center;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .action-btn.view-btn {
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--admin-primary);
        }

        .action-btn.edit-btn {
            background-color: rgba(255, 149, 0, 0.1);
            color: var(--admin-warning);
        }

        .action-btn.delete-btn {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--admin-danger);
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        .action-btn.view-btn:hover {
            background-color: var(--admin-primary);
            color: white;
        }

        .action-btn.edit-btn:hover {
            background-color: var(--admin-warning);
            color: white;
        }

        .action-btn.delete-btn:hover {
            background-color: var(--admin-danger);
            color: white;
        }

        /* Add invitation button enhancement */
        .add-new-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            text-decoration: none;
            transition: all 0.3s;
        }

        .add-new-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</body>
</html>
