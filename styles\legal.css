/* Legal Pages Styles (Terms, Privacy, FAQ) */

.legal-section {
    padding: 150px 0 100px;
}

.legal-container {
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.legal-header {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 40px;
    text-align: center;
}

.legal-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.legal-header p {
    font-size: 1rem;
    opacity: 0.8;
}

.legal-content {
    display: flex;
    padding: 0;
}

.legal-sidebar {
    width: 280px;
    background-color: var(--light-bg);
    padding: 30px 20px;
    border-left: 1px solid var(--border-color);
}

.legal-sidebar ul {
    position: sticky;
    top: 100px;
}

.legal-sidebar li {
    margin-bottom: 15px;
}

.legal-sidebar a {
    color: var(--text-color);
    transition: var(--transition);
    display: block;
    padding: 8px 15px;
    border-radius: 5px;
}

.legal-sidebar a:hover,
.legal-sidebar a.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.legal-text {
    flex: 1;
    padding: 40px;
}

.legal-text section {
    margin-bottom: 40px;
    scroll-margin-top: 100px;
}

.legal-text h2 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.legal-text h3 {
    font-size: 1.4rem;
    margin: 25px 0 15px;
    color: var(--text-color);
}

.legal-text p {
    margin-bottom: 15px;
    line-height: 1.7;
}

.legal-text ul, .legal-text ol {
    margin: 15px 0;
    padding-right: 20px;
}

.legal-text li {
    margin-bottom: 10px;
    list-style-position: inside;
}

.legal-text ul li {
    list-style-type: disc;
}

.legal-text ol li {
    list-style-type: decimal;
}

.legal-text a {
    color: var(--primary-color);
    text-decoration: underline;
}

.legal-text a:hover {
    text-decoration: none;
}

/* FAQ Specific Styles */
.faq-container {
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.faq-header {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 40px;
    text-align: center;
}

.faq-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.faq-header p {
    font-size: 1rem;
    opacity: 0.8;
}

.faq-content {
    padding: 40px;
}

.faq-categories {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 40px;
}

.faq-category-btn {
    padding: 10px 20px;
    background-color: var(--light-bg);
    border: none;
    border-radius: 30px;
    cursor: pointer;
    font-family: 'Tajawal', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
    transition: var(--transition);
}

.faq-category-btn:hover,
.faq-category-btn.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.faq-list {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 20px;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 15px;
    background-color: var(--light-bg);
    border-radius: 5px;
    transition: var(--transition);
}

.faq-question h3 {
    font-size: 1.2rem;
    margin: 0;
}

.faq-question .icon {
    font-size: 1.2rem;
    transition: var(--transition);
}

.faq-question.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.faq-question.active .icon {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 15px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-answer.active {
    max-height: 500px;
    padding: 15px;
}

.faq-answer p {
    margin-bottom: 15px;
    line-height: 1.7;
}

.faq-answer ul {
    margin: 15px 0;
    padding-right: 20px;
}

.faq-answer li {
    margin-bottom: 10px;
    list-style-type: disc;
    list-style-position: inside;
}

.faq-contact {
    text-align: center;
    margin-top: 50px;
    padding: 30px;
    background-color: var(--light-bg);
    border-radius: 10px;
}

.faq-contact h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.faq-contact p {
    margin-bottom: 20px;
}

/* Responsive Styles */
@media screen and (max-width: 992px) {
    .legal-content {
        flex-direction: column;
    }
    
    .legal-sidebar {
        width: 100%;
        border-left: none;
        border-bottom: 1px solid var(--border-color);
    }
    
    .legal-sidebar ul {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        position: static;
    }
    
    .legal-sidebar li {
        margin-bottom: 0;
    }
    
    .legal-sidebar a {
        padding: 5px 10px;
        font-size: 0.9rem;
    }
}

@media screen and (max-width: 768px) {
    .legal-section {
        padding: 120px 0 60px;
    }
    
    .legal-header {
        padding: 30px 20px;
    }
    
    .legal-header h1 {
        font-size: 2rem;
    }
    
    .legal-text {
        padding: 30px 20px;
    }
    
    .legal-text h2 {
        font-size: 1.5rem;
    }
    
    .legal-text h3 {
        font-size: 1.2rem;
    }
    
    .faq-header {
        padding: 30px 20px;
    }
    
    .faq-header h1 {
        font-size: 2rem;
    }
    
    .faq-content {
        padding: 30px 20px;
    }
    
    .faq-question h3 {
        font-size: 1.1rem;
    }
}
