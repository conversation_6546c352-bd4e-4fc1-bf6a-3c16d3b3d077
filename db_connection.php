<?php
/**
 * ملف اتصال قاعدة البيانات
 * يستخدم لإنشاء اتصال بقاعدة البيانات
 */

// استيراد ملف الإعدادات
require_once 'config.php';

/**
 * دالة للاتصال بقاعدة البيانات
 * @return mysqli|false كائن اتصال قاعدة البيانات أو false في حالة فشل الاتصال
 */
function connectDB() {
    // التحقق من وجود إعدادات قاعدة البيانات
    if (empty(DB_HOST) || empty(DB_USER) || empty(DB_NAME)) {
        return false;
    }
    
    // إنشاء اتصال جديد
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    // التحقق من وجود أخطاء في الاتصال
    if ($conn->connect_error) {
        if (DEBUG) {
            die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
        } else {
            die("فشل الاتصال بقاعدة البيانات. يرجى التحقق من إعدادات الاتصال.");
        }
    }
    
    // تعيين ترميز الاتصال
    $conn->set_charset(DB_CHARSET);
    
    return $conn;
}

/**
 * دالة لتنفيذ استعلام SQL
 * @param string $sql استعلام SQL
 * @return mysqli_result|bool نتيجة الاستعلام أو false في حالة فشل الاستعلام
 */
function executeQuery($sql) {
    $conn = connectDB();
    
    if (!$conn) {
        return false;
    }
    
    $result = $conn->query($sql);
    
    if (!$result && DEBUG) {
        echo "خطأ في تنفيذ الاستعلام: " . $conn->error;
    }
    
    return $result;
}

/**
 * دالة لتنفيذ استعلام SQL مع حماية من حقن SQL
 * @param string $sql استعلام SQL مع علامات استفهام للمعلمات
 * @param string $types أنواع المعلمات (s للنصوص، i للأرقام الصحيحة، d للأرقام العشرية، b للبيانات الثنائية)
 * @param array $params مصفوفة المعلمات
 * @return mysqli_stmt|bool كائن الاستعلام المعد أو false في حالة فشل الاستعلام
 */
function executePreparedQuery($sql, $types, $params) {
    $conn = connectDB();
    
    if (!$conn) {
        return false;
    }
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        if (DEBUG) {
            echo "خطأ في إعداد الاستعلام: " . $conn->error;
        }
        return false;
    }
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    
    if ($stmt->error && DEBUG) {
        echo "خطأ في تنفيذ الاستعلام: " . $stmt->error;
    }
    
    return $stmt;
}

/**
 * دالة للحصول على صف واحد من نتيجة الاستعلام
 * @param string $sql استعلام SQL
 * @return array|null مصفوفة تحتوي على بيانات الصف أو null في حالة عدم وجود نتائج
 */
function fetchRow($sql) {
    $result = executeQuery($sql);
    
    if (!$result) {
        return null;
    }
    
    $row = $result->fetch_assoc();
    $result->free();
    
    return $row;
}

/**
 * دالة للحصول على جميع الصفوف من نتيجة الاستعلام
 * @param string $sql استعلام SQL
 * @return array مصفوفة تحتوي على جميع الصفوف
 */
function fetchAll($sql) {
    $result = executeQuery($sql);
    
    if (!$result) {
        return [];
    }
    
    $rows = [];
    
    while ($row = $result->fetch_assoc()) {
        $rows[] = $row;
    }
    
    $result->free();
    
    return $rows;
}

/**
 * دالة للحصول على عدد الصفوف المتأثرة بالاستعلام
 * @return int عدد الصفوف المتأثرة
 */
function getAffectedRows() {
    $conn = connectDB();
    
    if (!$conn) {
        return 0;
    }
    
    return $conn->affected_rows;
}

/**
 * دالة للحصول على معرف آخر صف تم إدراجه
 * @return int معرف آخر صف تم إدراجه
 */
function getLastInsertId() {
    $conn = connectDB();
    
    if (!$conn) {
        return 0;
    }
    
    return $conn->insert_id;
}

/**
 * دالة لإغلاق اتصال قاعدة البيانات
 */
function closeDB() {
    $conn = connectDB();
    
    if ($conn) {
        $conn->close();
    }
}

/**
 * دالة لتنظيف البيانات المدخلة لمنع حقن SQL
 * @param string $data البيانات المدخلة
 * @return string البيانات بعد التنظيف
 */
function sanitizeInput($data) {
    $conn = connectDB();
    
    if (!$conn) {
        return $data;
    }
    
    return $conn->real_escape_string($data);
}
