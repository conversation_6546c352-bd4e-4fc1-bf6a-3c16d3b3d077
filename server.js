const http = require('http');
const fs = require('fs');
const path = require('path');
const querystring = require('querystring');

const PORT = 3000;

const MIME_TYPES = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'text/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
};

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir);
}

// Create a directory for QR codes if it doesn't exist
const qrCodesDir = path.join(__dirname, 'qrcodes');
if (!fs.existsSync(qrCodesDir)) {
    fs.mkdirSync(qrCodesDir);
}

const server = http.createServer((req, res) => {
    console.log(`Request: ${req.method} ${req.url}`);

    // Handle CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // Handle OPTIONS request for CORS preflight
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    // Handle API endpoints
    if (req.url.startsWith('/api/')) {
        handleApiRequest(req, res);
        return;
    }

    // Normalize URL by removing query string and trailing slash
    let url = req.url.split('?')[0];
    if (url.endsWith('/') && url.length > 1) {
        url = url.slice(0, -1);
    }

    // Default to index.html for root path
    if (url === '/') {
        url = '/index.html';
    }

    // Resolve the file path
    const filePath = path.join(__dirname, url);
    const ext = path.extname(filePath);

    // Check if the file exists
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            console.error(`File not found: ${filePath}`);
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('404 Not Found');
            return;
        }

        // Read and serve the file
        fs.readFile(filePath, (err, data) => {
            if (err) {
                console.error(`Error reading file: ${err}`);
                res.writeHead(500, { 'Content-Type': 'text/plain' });
                res.end('500 Internal Server Error');
                return;
            }

            // Set the content type based on file extension
            const contentType = MIME_TYPES[ext] || 'application/octet-stream';
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(data);
        });
    });
});

/**
 * Handle API requests
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleApiRequest(req, res) {
    const url = req.url;

    // Handle file upload
    if (url === '/api/upload' && req.method === 'POST') {
        handleFileUpload(req, res);
    }
    // Handle QR code generation
    else if (url === '/api/generate-qrcode' && req.method === 'POST') {
        handleQRCodeGeneration(req, res);
    }
    // Handle unknown API endpoints
    else {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Endpoint not found' }));
    }
}

/**
 * Handle file upload
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleFileUpload(req, res) {
    // This is a simple implementation for demonstration purposes
    // In a real application, you would use a library like formidable or multer

    let body = [];

    req.on('data', (chunk) => {
        body.push(chunk);
    });

    req.on('end', () => {
        try {
            // Parse the multipart form data (simplified version)
            const buffer = Buffer.concat(body);

            // Generate a unique filename
            const filename = `upload_${Date.now()}.jpg`;
            const filepath = path.join(uploadsDir, filename);

            // Save the file
            fs.writeFile(filepath, buffer, (err) => {
                if (err) {
                    console.error(`Error saving file: ${err}`);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'Failed to save file' }));
                    return;
                }

                // Return success response
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: true,
                    filename: filename,
                    url: `/uploads/${filename}`
                }));
            });
        } catch (err) {
            console.error(`Error processing upload: ${err}`);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request' }));
        }
    });
}

/**
 * Handle QR code generation
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleQRCodeGeneration(req, res) {
    let body = '';

    req.on('data', (chunk) => {
        body += chunk.toString();
    });

    req.on('end', () => {
        try {
            const data = JSON.parse(body);

            if (!data.content) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Missing content parameter' }));
                return;
            }

            // In a real application, you would generate a QR code image here
            // For this example, we'll just return a success response

            // Generate a unique filename
            const filename = `qrcode_${Date.now()}.png`;
            const filepath = path.join(qrCodesDir, filename);

            // Return success response
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                filename: filename,
                url: `/qrcodes/${filename}`
            }));
        } catch (err) {
            console.error(`Error processing QR code generation: ${err}`);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request' }));
        }
    });
}

server.listen(PORT, () => {
    console.log(`Server running at http://localhost:${PORT}/`);
});
