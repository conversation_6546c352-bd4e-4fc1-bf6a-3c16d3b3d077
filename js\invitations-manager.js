/**
 * Invitations Manager Module
 * This module handles invitation management functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const addInvitationBtn = document.querySelector('.add-new-btn');
    const viewButtons = document.querySelectorAll('.view-btn');
    const editButtons = document.querySelectorAll('.edit-btn');
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const statusFilter = document.getElementById('status-filter');
    const typeFilter = document.getElementById('type-filter');
    const dateFilter = document.getElementById('date-filter');
    const searchInput = document.querySelector('.search-wrapper input');
    const searchBtn = document.querySelector('.search-wrapper button');
    
    // Initialize the invitations manager
    initInvitationsManager();
    
    /**
     * Initialize the invitations manager
     */
    function initInvitationsManager() {
        // Add event listeners
        if (addInvitationBtn) {
            addInvitationBtn.addEventListener('click', function() {
                window.location.href = '../create-invitation.html';
            });
        }
        
        // View invitation details
        if (viewButtons) {
            viewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const invitationId = getInvitationIdFromButton(this);
                    viewInvitation(invitationId);
                });
            });
        }
        
        // Edit invitation
        if (editButtons) {
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const invitationId = getInvitationIdFromButton(this);
                    editInvitation(invitationId);
                });
            });
        }
        
        // Delete invitation
        if (deleteButtons) {
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const invitationId = getInvitationIdFromButton(this);
                    deleteInvitation(invitationId);
                });
            });
        }
        
        // Filter invitations
        if (statusFilter) {
            statusFilter.addEventListener('change', filterInvitations);
        }
        
        if (typeFilter) {
            typeFilter.addEventListener('change', filterInvitations);
        }
        
        if (dateFilter) {
            dateFilter.addEventListener('change', filterInvitations);
        }
        
        // Search invitations
        if (searchBtn) {
            searchBtn.addEventListener('click', searchInvitations);
        }
        
        if (searchInput) {
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchInvitations();
                }
            });
        }
    }
    
    /**
     * Get invitation ID from button
     * @param {HTMLElement} button - The button element
     * @returns {string} - The invitation ID
     */
    function getInvitationIdFromButton(button) {
        // In a real application, you would get the invitation ID from a data attribute
        // For now, we'll just get it from the row
        const row = button.closest('tr');
        const invitationTitle = row.querySelector('td:nth-child(2)').textContent;
        
        // Return a dummy ID based on the title
        return invitationTitle.replace(/\s+/g, '-').toLowerCase();
    }
    
    /**
     * View invitation details
     * @param {string} invitationId - The invitation ID
     */
    function viewInvitation(invitationId) {
        // In a real application, you would redirect to the invitation details page
        // For now, we'll just show an alert
        alert(`عرض تفاصيل الدعوة: ${invitationId}`);
        
        // Create and show modal with invitation details
        showInvitationModal(invitationId, 'view');
    }
    
    /**
     * Edit invitation
     * @param {string} invitationId - The invitation ID
     */
    function editInvitation(invitationId) {
        // In a real application, you would redirect to the edit invitation page
        // For now, we'll just show an alert
        alert(`تعديل الدعوة: ${invitationId}`);
        
        // Redirect to create invitation page with invitation ID
        window.location.href = `../create-invitation.html?id=${invitationId}`;
    }
    
    /**
     * Delete invitation
     * @param {string} invitationId - The invitation ID
     */
    function deleteInvitation(invitationId) {
        // Ask for confirmation
        if (confirm(`هل أنت متأكد من حذف الدعوة: ${invitationId}؟`)) {
            // In a real application, you would send a request to the server
            // For now, we'll just remove the row from the table
            const row = document.querySelector(`tr[data-id="${invitationId}"]`);
            
            if (row) {
                row.remove();
                alert('تم حذف الدعوة بنجاح');
            } else {
                // If we can't find the row by data-id, try to find it by the invitation title
                const rows = document.querySelectorAll('tbody tr');
                
                for (let i = 0; i < rows.length; i++) {
                    const title = rows[i].querySelector('td:nth-child(2)').textContent;
                    const id = title.replace(/\s+/g, '-').toLowerCase();
                    
                    if (id === invitationId) {
                        rows[i].remove();
                        alert('تم حذف الدعوة بنجاح');
                        break;
                    }
                }
            }
        }
    }
    
    /**
     * Filter invitations
     */
    function filterInvitations() {
        // Get filter values
        const status = statusFilter.value;
        const type = typeFilter.value;
        const date = dateFilter.value;
        
        // In a real application, you would send a request to the server
        // For now, we'll just show an alert
        alert(`تصفية الدعوات: الحالة=${status}, النوع=${type}, التاريخ=${date}`);
    }
    
    /**
     * Search invitations
     */
    function searchInvitations() {
        // Get search value
        const searchValue = searchInput.value;
        
        // In a real application, you would send a request to the server
        // For now, we'll just show an alert
        alert(`البحث عن الدعوات: ${searchValue}`);
    }
    
    /**
     * Show invitation modal
     * @param {string} invitationId - The invitation ID
     * @param {string} mode - The modal mode (view, edit)
     */
    function showInvitationModal(invitationId, mode) {
        // Create modal element
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'invitation-modal';
        
        // Get invitation data (in a real application, you would fetch this from the server)
        const invitationData = getInvitationData(invitationId);
        
        // Create modal content
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>${mode === 'view' ? 'تفاصيل الدعوة' : 'تعديل الدعوة'}</h2>
                    <button class="close-modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="invitation-details">
                        <div class="invitation-header">
                            <h3>${invitationData.title}</h3>
                            <span class="status ${invitationData.status}">${getStatusText(invitationData.status)}</span>
                        </div>
                        
                        <div class="invitation-info">
                            <div class="info-group">
                                <label>نوع المناسبة:</label>
                                <span>${invitationData.type}</span>
                            </div>
                            
                            <div class="info-group">
                                <label>تاريخ المناسبة:</label>
                                <span>${invitationData.date}</span>
                            </div>
                            
                            <div class="info-group">
                                <label>وقت المناسبة:</label>
                                <span>${invitationData.time}</span>
                            </div>
                            
                            <div class="info-group">
                                <label>مكان المناسبة:</label>
                                <span>${invitationData.location}</span>
                            </div>
                            
                            <div class="info-group">
                                <label>عدد المدعوين:</label>
                                <span>${invitationData.guestsCount}</span>
                            </div>
                            
                            <div class="info-group">
                                <label>نسبة التأكيد:</label>
                                <div class="progress-bar">
                                    <div class="progress" style="width: ${invitationData.confirmationRate}%;">${invitationData.confirmationRate}%</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="invitation-preview">
                            <h4>معاينة الدعوة</h4>
                            <div class="preview-frame">
                                <iframe src="../templates/${invitationData.template}/index.html" frameborder="0"></iframe>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary close-btn">إغلاق</button>
                    ${mode === 'view' ? `<button class="btn btn-primary edit-btn">تعديل</button>` : `<button class="btn btn-primary save-btn">حفظ</button>`}
                </div>
            </div>
        `;
        
        // Add modal to the page
        document.body.appendChild(modal);
        
        // Show modal
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        
        // Add event listeners
        const closeButtons = modal.querySelectorAll('.close-modal, .close-btn');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                closeModal(modal);
            });
        });
        
        // Edit button
        const editBtn = modal.querySelector('.edit-btn');
        if (editBtn) {
            editBtn.addEventListener('click', function() {
                closeModal(modal);
                editInvitation(invitationId);
            });
        }
        
        // Save button
        const saveBtn = modal.querySelector('.save-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', function() {
                // In a real application, you would save the changes
                alert('تم حفظ التغييرات بنجاح');
                closeModal(modal);
            });
        }
    }
    
    /**
     * Close modal
     * @param {HTMLElement} modal - The modal element
     */
    function closeModal(modal) {
        modal.classList.remove('show');
        
        // Remove modal after animation
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
    
    /**
     * Get invitation data
     * @param {string} invitationId - The invitation ID
     * @returns {Object} - The invitation data
     */
    function getInvitationData(invitationId) {
        // In a real application, you would fetch this from the server
        // For now, we'll just return dummy data
        return {
            id: invitationId,
            title: invitationId.replace(/-/g, ' '),
            status: 'active',
            type: 'زفاف',
            date: '15/05/2025',
            time: '19:00',
            location: 'قاعة الأميرات، الرياض',
            guestsCount: 250,
            confirmationRate: 85,
            template: 'wedding-elegant'
        };
    }
    
    /**
     * Get status text
     * @param {string} status - The status code
     * @returns {string} - The status text
     */
    function getStatusText(status) {
        switch (status) {
            case 'active':
                return 'نشطة';
            case 'completed':
                return 'مكتملة';
            case 'cancelled':
                return 'ملغية';
            default:
                return status;
        }
    }
});
