/**
 * Template Preview Module
 * This module handles template preview functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const deviceButtons = document.querySelectorAll('.device-btn');
    const previewContainer = document.querySelector('.preview-frame-container');
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    const iframe = document.querySelector('.preview-frame iframe');
    const useTemplateBtn = document.getElementById('use-template-btn');
    const previewMobileBtn = document.getElementById('preview-mobile-btn');
    const similarTemplates = document.querySelectorAll('.similar-template');

    // Customization elements
    const primaryColorInput = document.getElementById('primary-color-preview');
    const secondaryColorInput = document.getElementById('secondary-color-preview');
    const fontFamilySelect = document.getElementById('font-family-preview');
    const applyPreviewBtn = document.getElementById('apply-preview-btn');

    // Get template ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const templateId = urlParams.get('template') || 'wedding-elegant';

    // Check if template exists
    checkTemplateExists(templateId);

    // Initialize the preview
    initPreview();

    /**
     * Initialize the preview
     */
    function initPreview() {
        // Set the iframe source based on the template ID
        if (iframe) {
            const iframeError = document.querySelector('.iframe-error');
            const reloadBtn = document.querySelector('.reload-btn');

            // Add event listener for reload button
            if (reloadBtn) {
                reloadBtn.addEventListener('click', function() {
                    // Hide error message
                    if (iframeError) {
                        iframeError.style.display = 'none';
                    }

                    // Reload iframe
                    iframe.src = `templates/${templateId}/index.html`;
                });
            }

            // Set iframe source
            iframe.src = `templates/${templateId}/index.html`;
            console.log('Setting iframe source to:', `templates/${templateId}/index.html`);

            // Add event listener to check if iframe loaded successfully
            iframe.addEventListener('load', function() {
                console.log('Iframe loaded successfully');

                // Hide error message if it's visible
                if (iframeError) {
                    iframeError.style.display = 'none';
                }

                // Try to access iframe content to verify it loaded correctly
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (!iframeDoc.body) {
                        throw new Error('Iframe body not found');
                    }
                } catch (error) {
                    console.error('Error accessing iframe content:', error);

                    // Show error message
                    if (iframeError) {
                        iframeError.style.display = 'flex';
                    }
                }
            });

            iframe.addEventListener('error', function(error) {
                console.error('Error loading iframe:', error);

                // Show error message
                if (iframeError) {
                    iframeError.style.display = 'flex';
                }
            });
        }

        // Device selector
        if (deviceButtons && previewContainer) {
            deviceButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    deviceButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Update preview container class
                    const device = this.getAttribute('data-device');
                    previewContainer.className = 'preview-frame-container ' + device;
                });
            });
        }

        // Fullscreen button
        if (fullscreenBtn && iframe) {
            fullscreenBtn.addEventListener('click', function() {
                if (iframe.requestFullscreen) {
                    iframe.requestFullscreen();
                } else if (iframe.webkitRequestFullscreen) { /* Safari */
                    iframe.webkitRequestFullscreen();
                } else if (iframe.msRequestFullscreen) { /* IE11 */
                    iframe.msRequestFullscreen();
                }
            });
        }

        // Use template button
        if (useTemplateBtn) {
            useTemplateBtn.addEventListener('click', function() {
                window.location.href = `create-invitation.html?template=${templateId}`;
            });
        }

        // Preview on mobile button
        if (previewMobileBtn && deviceButtons) {
            previewMobileBtn.addEventListener('click', function() {
                // Simulate mobile view
                deviceButtons.forEach(btn => {
                    if (btn.getAttribute('data-device') === 'mobile') {
                        btn.click();
                    }
                });
            });
        }

        // Similar templates click
        if (similarTemplates) {
            similarTemplates.forEach(template => {
                template.addEventListener('click', function() {
                    const newTemplateId = this.getAttribute('data-template-id');
                    if (newTemplateId) {
                        window.location.href = `template-preview.html?template=${newTemplateId}`;
                    } else {
                        alert('سيتم الانتقال إلى القالب المحدد');
                    }
                });
            });
        }

        // Apply customization preview
        if (applyPreviewBtn && iframe) {
            applyPreviewBtn.addEventListener('click', function() {
                applyCustomizationPreview();
            });
        }
    }

    /**
     * Load template data
     * @param {string} templateId - The template ID to load
     */
    function loadTemplateData(templateId) {
        // In a real application, you would fetch the template data from the server
        // For now, we'll just use some sample data

        const templateData = {
            'wedding-elegant': {
                name: 'قالب زفاف أنيق',
                description: 'قالب أنيق لحفلات الزفاف مع تصميم عصري وألوان ذهبية فاخرة.',
                category: 'زفاف',
                style: 'أنيق',
                color: 'ذهبي',
                thumbnail: 'images/templates/template1.jpg',
                similar: ['wedding-classic', 'wedding-gold', 'wedding-simple']
            },
            'wedding-classic': {
                name: 'قالب زفاف كلاسيكي',
                description: 'قالب كلاسيكي لحفلات الزفاف مع تصميم تقليدي وألوان أنيقة.',
                category: 'زفاف',
                style: 'كلاسيكي',
                color: 'أبيض',
                thumbnail: 'images/templates/template2.jpg',
                similar: ['wedding-elegant', 'wedding-gold', 'wedding-simple']
            },
            'wedding-gold': {
                name: 'قالب زفاف ذهبي',
                description: 'قالب فاخر لحفلات الزفاف مع تصميم ذهبي مميز.',
                category: 'زفاف',
                style: 'أنيق',
                color: 'ذهبي',
                thumbnail: 'images/templates/template3.jpg',
                similar: ['wedding-elegant', 'wedding-classic', 'wedding-simple']
            },
            'wedding-simple': {
                name: 'قالب زفاف بسيط',
                description: 'قالب بسيط وأنيق لحفلات الزفاف مع تصميم مينيمال.',
                category: 'زفاف',
                style: 'بسيط',
                color: 'أبيض',
                thumbnail: 'images/templates/template4.jpg',
                similar: ['wedding-elegant', 'wedding-classic', 'wedding-gold']
            }
        };

        return templateData[templateId] || templateData['wedding-elegant'];
    }

    /**
     * Update template information
     * @param {Object} templateData - The template data
     */
    function updateTemplateInfo(templateData) {
        // Update template name
        const templateName = document.querySelector('.template-info h1');
        if (templateName) {
            templateName.textContent = templateData.name;
        }

        // Update template description
        const templateDescription = document.querySelector('.template-info p');
        if (templateDescription) {
            templateDescription.textContent = templateData.description;
        }

        // Update template meta
        const templateCategory = document.querySelector('.template-meta .template-category');
        if (templateCategory) {
            templateCategory.textContent = templateData.category;
        }

        const templateStyle = document.querySelector('.template-meta .template-style');
        if (templateStyle) {
            templateStyle.textContent = templateData.style;
        }

        const templateColor = document.querySelector('.template-meta .template-color');
        if (templateColor) {
            templateColor.textContent = templateData.color;
        }

        // Update similar templates
        const similarTemplatesContainer = document.querySelector('.similar-templates');
        if (similarTemplatesContainer && templateData.similar) {
            similarTemplatesContainer.innerHTML = '';

            templateData.similar.forEach(similarId => {
                const similarData = loadTemplateData(similarId);

                const similarTemplate = document.createElement('div');
                similarTemplate.className = 'similar-template';
                similarTemplate.setAttribute('data-template-id', similarId);

                similarTemplate.innerHTML = `
                    <img src="${similarData.thumbnail}" alt="${similarData.name}">
                    <div class="similar-template-info">
                        <h4>${similarData.name}</h4>
                        <div class="template-meta">
                            <span class="template-category">${similarData.category}</span>
                            <span class="template-style">${similarData.style}</span>
                        </div>
                    </div>
                `;

                similarTemplatesContainer.appendChild(similarTemplate);

                // Add click event
                similarTemplate.addEventListener('click', function() {
                    window.location.href = `template-preview.html?template=${similarId}`;
                });
            });
        }
    }

    /**
     * Check if template exists
     * @param {string} templateId - The template ID to check
     */
    function checkTemplateExists(templateId) {
        // Create a new XMLHttpRequest
        const xhr = new XMLHttpRequest();

        // Configure it to do a HEAD request to the template file
        xhr.open('HEAD', `templates/${templateId}/index.html`, true);

        // Set up the onload callback
        xhr.onload = function() {
            if (xhr.status === 200) {
                console.log(`Template ${templateId} exists`);
            } else {
                console.error(`Template ${templateId} does not exist (status: ${xhr.status})`);
                alert(`القالب المطلوب غير موجود. سيتم استخدام القالب الافتراضي.`);

                // Use default template
                if (iframe) {
                    iframe.src = 'templates/wedding-elegant/index.html';
                }
            }
        };

        // Set up the onerror callback
        xhr.onerror = function() {
            console.error(`Error checking if template ${templateId} exists`);
            alert(`حدث خطأ أثناء التحقق من وجود القالب. سيتم استخدام القالب الافتراضي.`);

            // Use default template
            if (iframe) {
                iframe.src = 'templates/wedding-elegant/index.html';
            }
        };

        // Send the request
        xhr.send();
    }

    /**
     * Apply customization preview to the template
     */
    function applyCustomizationPreview() {
        if (!iframe) return;

        // Get the iframe document
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

        // Get customization values
        const primaryColor = primaryColorInput.value;
        const secondaryColor = secondaryColorInput.value;
        const fontFamily = fontFamilySelect.value;

        // Create a style element
        let styleElement = iframeDoc.getElementById('preview-custom-styles');

        if (!styleElement) {
            styleElement = iframeDoc.createElement('style');
            styleElement.id = 'preview-custom-styles';
            iframeDoc.head.appendChild(styleElement);
        }

        // Set the custom styles
        styleElement.textContent = `
            :root {
                --primary-color: ${primaryColor};
                --secondary-color: ${secondaryColor};
                --font-family: '${fontFamily}', sans-serif;
            }

            body {
                font-family: var(--font-family);
            }

            .primary-color, .btn-primary, .bg-primary {
                background-color: var(--primary-color) !important;
                border-color: var(--primary-color) !important;
            }

            .secondary-color, .btn-secondary, .bg-secondary {
                background-color: var(--secondary-color) !important;
                border-color: var(--secondary-color) !important;
            }

            .text-primary {
                color: var(--primary-color) !important;
            }

            .text-secondary {
                color: var(--secondary-color) !important;
            }
        `;

        // Show success message
        alert('تم تطبيق التخصيصات بنجاح');
    }

    // Load and update template data
    const templateData = loadTemplateData(templateId);
    updateTemplateInfo(templateData);
});
