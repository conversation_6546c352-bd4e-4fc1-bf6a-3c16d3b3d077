<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين | دعوات إلكترونية</title>
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/responsive.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Tajawal is a good Arabic font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/admin.css">
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <div class="admin-logo">
                    <div class="text-logo">دعوات إلكترونية</div>
                </div>
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="صورة المستخدم">
                </div>
                <div class="user-info">
                    <h3>أحمد محمد</h3>
                    <p>مدير النظام</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="index.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>المستخدمين</span>
                        </a>
                    </li>
                    <li>
                        <a href="invitations.html">
                            <i class="fas fa-envelope-open-text"></i>
                            <span>الدعوات</span>
                        </a>
                    </li>
                    <li>
                        <a href="templates.html">
                            <i class="fas fa-paint-brush"></i>
                            <span>قوالب التصميم</span>
                        </a>
                    </li>
                    <li>
                        <a href="packages.html">
                            <i class="fas fa-box"></i>
                            <span>الباقات</span>
                        </a>
                    </li>
                    <li>
                        <a href="payments.html">
                            <i class="fas fa-credit-card"></i>
                            <span>المدفوعات</span>
                        </a>
                    </li>
                    <li>
                        <a href="reports.html">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <a href="login.html" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="بحث...">
                </div>
                
                <div class="header-actions">
                    <div class="header-notification">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="header-message">
                        <i class="fas fa-envelope"></i>
                        <span class="message-badge">5</span>
                    </div>
                    <div class="header-profile">
                        <img src="images/admin-avatar.png" alt="صورة المستخدم">
                    </div>
                </div>
            </header>
            
            <div class="admin-content">
                <div class="page-header">
                    <h1>إدارة المستخدمين</h1>
                    <button class="btn btn-primary add-new-btn">
                        <i class="fas fa-plus"></i>
                        إضافة مستخدم جديد
                    </button>
                </div>
                
                <!-- Users Filters -->
                <div class="filters-container">
                    <div class="filters-wrapper">
                        <div class="filter-group">
                            <label for="status-filter">الحالة</label>
                            <select id="status-filter">
                                <option value="all">الكل</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="suspended">معلق</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="package-filter">الباقة</label>
                            <select id="package-filter">
                                <option value="all">الكل</option>
                                <option value="basic">الأساسية</option>
                                <option value="premium">المميزة</option>
                                <option value="business">البريميوم</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="date-filter">تاريخ التسجيل</label>
                            <select id="date-filter">
                                <option value="all">الكل</option>
                                <option value="today">اليوم</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                                <option value="year">هذا العام</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="search-wrapper">
                        <input type="text" placeholder="بحث عن مستخدم...">
                        <button class="btn btn-primary">بحث</button>
                    </div>
                </div>
                
                <!-- Users Table -->
                <div class="data-table users-table">
                    <div class="table-content">
                        <table>
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="select-all">
                                    </th>
                                    <th>المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الجوال</th>
                                    <th>الباقة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox">
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user1.png" alt="صورة المستخدم">
                                            <span>سعد العتيبي</span>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td>+966 50 123 4567</td>
                                    <td>الأساسية</td>
                                    <td>01/05/2025</td>
                                    <td><span class="status active">نشط</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox">
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user2.png" alt="صورة المستخدم">
                                            <span>نورة الشمري</span>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td>+966 55 987 6543</td>
                                    <td>المميزة</td>
                                    <td>30/04/2025</td>
                                    <td><span class="status active">نشط</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox">
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user3.png" alt="صورة المستخدم">
                                            <span>محمد السالم</span>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td>+966 54 456 7890</td>
                                    <td>المميزة</td>
                                    <td>29/04/2025</td>
                                    <td><span class="status active">نشط</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox">
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user4.png" alt="صورة المستخدم">
                                            <span>فاطمة الزهراني</span>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td>+966 56 789 0123</td>
                                    <td>الأساسية</td>
                                    <td>28/04/2025</td>
                                    <td><span class="status inactive">غير نشط</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox">
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user5.png" alt="صورة المستخدم">
                                            <span>عبدالله الدوسري</span>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td>+966 59 321 6547</td>
                                    <td>البريميوم</td>
                                    <td>27/04/2025</td>
                                    <td><span class="status active">نشط</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox">
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user6.png" alt="صورة المستخدم">
                                            <span>سارة القحطاني</span>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td>+966 58 654 3210</td>
                                    <td>الأساسية</td>
                                    <td>26/04/2025</td>
                                    <td><span class="status suspended">معلق</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox">
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user7.png" alt="صورة المستخدم">
                                            <span>خالد المالكي</span>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td>+966 53 987 1234</td>
                                    <td>المميزة</td>
                                    <td>25/04/2025</td>
                                    <td><span class="status active">نشط</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox">
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <img src="images/user8.png" alt="صورة المستخدم">
                                            <span>منى الحربي</span>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td>+966 57 456 7891</td>
                                    <td>الأساسية</td>
                                    <td>24/04/2025</td>
                                    <td><span class="status inactive">غير نشط</span></td>
                                    <td>
                                        <div class="actions">
                                            <button class="action-btn view-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Pagination -->
                <div class="pagination">
                    <div class="pagination-info">
                        <p>عرض 1-8 من 125 مستخدم</p>
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn">4</button>
                        <button class="pagination-btn">5</button>
                        <button class="pagination-btn">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                    <div class="pagination-options">
                        <label for="rows-per-page">عرض</label>
                        <select id="rows-per-page">
                            <option value="8">8</option>
                            <option value="16">16</option>
                            <option value="24">24</option>
                            <option value="32">32</option>
                        </select>
                        <span>لكل صفحة</span>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- User Modal -->
    <div class="modal" id="user-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>إضافة مستخدم جديد</h2>
                <button class="close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="user-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="user-name">الاسم الكامل</label>
                            <input type="text" id="user-name" name="user-name" placeholder="أدخل الاسم الكامل" required>
                        </div>
                        <div class="form-group">
                            <label for="user-email">البريد الإلكتروني</label>
                            <input type="email" id="user-email" name="user-email" placeholder="أدخل البريد الإلكتروني" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="user-phone">رقم الجوال</label>
                            <input type="tel" id="user-phone" name="user-phone" placeholder="أدخل رقم الجوال" required>
                        </div>
                        <div class="form-group">
                            <label for="user-password">كلمة المرور</label>
                            <input type="password" id="user-password" name="user-password" placeholder="أدخل كلمة المرور" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="user-package">الباقة</label>
                            <select id="user-package" name="user-package" required>
                                <option value="" disabled selected>اختر الباقة</option>
                                <option value="basic">الأساسية</option>
                                <option value="premium">المميزة</option>
                                <option value="business">البريميوم</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="user-status">الحالة</label>
                            <select id="user-status" name="user-status" required>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="suspended">معلق</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="user-notes">ملاحظات</label>
                        <textarea id="user-notes" name="user-notes" placeholder="أدخل أي ملاحظات إضافية"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary cancel-btn">إلغاء</button>
                <button class="btn btn-primary save-btn">حفظ</button>
            </div>
        </div>
    </div>
    
    <script src="../js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle sidebar
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            const adminContainer = document.querySelector('.admin-container');
            
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    adminContainer.classList.toggle('sidebar-collapsed');
                });
            }
            
            // Select all checkboxes
            const selectAllCheckbox = document.getElementById('select-all');
            const userCheckboxes = document.querySelectorAll('.user-checkbox');
            
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    userCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                });
            }
            
            // Modal functionality
            const addNewBtn = document.querySelector('.add-new-btn');
            const userModal = document.getElementById('user-modal');
            const closeModal = document.querySelector('.close-modal');
            const cancelBtn = document.querySelector('.cancel-btn');
            
            if (addNewBtn && userModal) {
                addNewBtn.addEventListener('click', function() {
                    userModal.classList.add('show');
                });
            }
            
            if (closeModal && userModal) {
                closeModal.addEventListener('click', function() {
                    userModal.classList.remove('show');
                });
            }
            
            if (cancelBtn && userModal) {
                cancelBtn.addEventListener('click', function() {
                    userModal.classList.remove('show');
                });
            }
            
            // Edit user functionality
            const editButtons = document.querySelectorAll('.edit-btn');
            
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Get user data from the row
                    const row = this.closest('tr');
                    const userName = row.querySelector('.user-info span').textContent;
                    const userEmail = row.cells[2].textContent;
                    const userPhone = row.cells[3].textContent;
                    const userPackage = row.cells[4].textContent;
                    const userStatus = row.querySelector('.status').classList.contains('active') ? 'active' : 
                                      row.querySelector('.status').classList.contains('inactive') ? 'inactive' : 'suspended';
                    
                    // Fill the form with user data
                    document.getElementById('user-name').value = userName;
                    document.getElementById('user-email').value = userEmail;
                    document.getElementById('user-phone').value = userPhone;
                    
                    // Set select options
                    const packageSelect = document.getElementById('user-package');
                    for (let i = 0; i < packageSelect.options.length; i++) {
                        if (packageSelect.options[i].text === userPackage) {
                            packageSelect.selectedIndex = i;
                            break;
                        }
                    }
                    
                    const statusSelect = document.getElementById('user-status');
                    for (let i = 0; i < statusSelect.options.length; i++) {
                        if (statusSelect.options[i].value === userStatus) {
                            statusSelect.selectedIndex = i;
                            break;
                        }
                    }
                    
                    // Change modal title
                    document.querySelector('.modal-header h2').textContent = 'تعديل بيانات المستخدم';
                    
                    // Show modal
                    userModal.classList.add('show');
                });
            });
            
            // Delete user functionality
            const deleteButtons = document.querySelectorAll('.delete-btn');
            
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                        // Delete user logic would go here
                        const row = this.closest('tr');
                        row.remove();
                    }
                });
            });
        });
    </script>
</body>
</html>
