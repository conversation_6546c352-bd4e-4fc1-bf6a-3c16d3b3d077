/**
 * Image Upload Module
 * This module handles image upload functionality for invitations
 */

const ImageUploader = {
    /**
     * Initialize the image uploader
     * @param {HTMLElement} fileInput - The file input element
     * @param {HTMLElement} previewContainer - The container to show the preview
     * @param {Object} options - Options for the uploader
     */
    init: function(fileInput, previewContainer, options = {}) {
        // Default options
        const defaultOptions = {
            maxFileSize: 5 * 1024 * 1024, // 5MB
            allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
            onFileSelected: null,
            onError: null
        };
        
        // Merge default options with provided options
        this.options = { ...defaultOptions, ...options };
        this.fileInput = fileInput;
        this.previewContainer = previewContainer;
        
        // Bind event listeners
        this.bindEvents();
    },
    
    /**
     * Bind event listeners to the file input
     */
    bindEvents: function() {
        this.fileInput.addEventListener('change', (e) => {
            this.handleFileSelect(e);
        });
        
        // Add drag and drop support
        if (this.previewContainer) {
            this.previewContainer.addEventListener('dragover', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.previewContainer.classList.add('dragover');
            });
            
            this.previewContainer.addEventListener('dragleave', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.previewContainer.classList.remove('dragover');
            });
            
            this.previewContainer.addEventListener('drop', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.previewContainer.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.fileInput.files = files;
                    this.handleFileSelect({ target: this.fileInput });
                }
            });
        }
    },
    
    /**
     * Handle file selection
     * @param {Event} e - The change event
     */
    handleFileSelect: function(e) {
        const file = e.target.files[0];
        
        if (!file) return;
        
        // Validate file
        const validationResult = this.validateFile(file);
        
        if (!validationResult.valid) {
            if (this.options.onError) {
                this.options.onError(validationResult.error);
            }
            return;
        }
        
        // Create preview
        this.createPreview(file);
        
        // Call the callback if provided
        if (this.options.onFileSelected) {
            this.options.onFileSelected(file);
        }
    },
    
    /**
     * Validate the selected file
     * @param {File} file - The file to validate
     * @returns {Object} - Validation result
     */
    validateFile: function(file) {
        // Check file type
        if (!this.options.allowedTypes.includes(file.type)) {
            return {
                valid: false,
                error: 'نوع الملف غير مدعوم. الأنواع المدعومة هي: JPEG, PNG, GIF'
            };
        }
        
        // Check file size
        if (file.size > this.options.maxFileSize) {
            return {
                valid: false,
                error: `حجم الملف كبير جدًا. الحد الأقصى هو ${this.options.maxFileSize / (1024 * 1024)}MB`
            };
        }
        
        return { valid: true };
    },
    
    /**
     * Create a preview of the selected file
     * @param {File} file - The file to preview
     */
    createPreview: function(file) {
        if (!this.previewContainer) return;
        
        // Clear the preview container
        this.previewContainer.innerHTML = '';
        
        // Create a preview image
        const img = document.createElement('img');
        img.classList.add('preview-image');
        
        // Create a file reader to read the file
        const reader = new FileReader();
        
        reader.onload = (e) => {
            img.src = e.target.result;
            this.previewContainer.appendChild(img);
            
            // Add remove button
            const removeBtn = document.createElement('button');
            removeBtn.classList.add('remove-image-btn');
            removeBtn.innerHTML = '<i class="fas fa-times"></i>';
            removeBtn.addEventListener('click', () => {
                this.removeImage();
            });
            
            this.previewContainer.appendChild(removeBtn);
        };
        
        reader.readAsDataURL(file);
    },
    
    /**
     * Remove the selected image
     */
    removeImage: function() {
        if (!this.previewContainer) return;
        
        // Clear the preview container
        this.previewContainer.innerHTML = '';
        
        // Reset the file input
        this.fileInput.value = '';
        
        // Add placeholder text
        const placeholder = document.createElement('div');
        placeholder.classList.add('upload-placeholder');
        placeholder.innerHTML = `
            <i class="fas fa-cloud-upload-alt"></i>
            <p>اسحب الصورة هنا أو انقر للاختيار</p>
        `;
        
        this.previewContainer.appendChild(placeholder);
    },
    
    /**
     * Upload the selected file to the server
     * @param {string} url - The URL to upload to
     * @param {Object} additionalData - Additional data to send with the file
     * @returns {Promise} - A promise that resolves with the server response
     */
    uploadFile: function(url, additionalData = {}) {
        return new Promise((resolve, reject) => {
            if (!this.fileInput.files[0]) {
                reject('لم يتم اختيار ملف');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', this.fileInput.files[0]);
            
            // Add additional data
            for (const key in additionalData) {
                formData.append(key, additionalData[key]);
            }
            
            // Create and send the request
            const xhr = new XMLHttpRequest();
            xhr.open('POST', url, true);
            
            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        resolve(xhr.responseText);
                    }
                } else {
                    reject('حدث خطأ أثناء رفع الملف');
                }
            };
            
            xhr.onerror = function() {
                reject('حدث خطأ أثناء رفع الملف');
            };
            
            xhr.send(formData);
        });
    }
};

// Export the image uploader
window.ImageUploader = ImageUploader;
