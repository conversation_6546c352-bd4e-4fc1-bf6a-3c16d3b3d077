<?php
/**
 * صفحة تسجيل الدخول للوحة التحكم
 */

// بدء الجلسة
session_start();

// استيراد ملف الإعدادات
require_once '../config.php';

// التحقق من حالة التثبيت
if (!defined('INSTALLED') || !INSTALLED) {
    // إعادة التوجيه إلى صفحة التثبيت
    header('Location: ../install_new.php');
    exit;
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once '../db_connection.php';

// التحقق مما إذا كان المستخدم مسجل الدخول بالفعل
if (isset($_SESSION['user_id']) && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin') {
    // إعادة التوجيه إلى لوحة التحكم
    header('Location: index.php');
    exit;
}

$error = '';
$email = '';

// معالجة نموذج تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);
    $password = trim($_POST['password']);
    
    // التحقق من صحة البيانات
    if (empty($email) || empty($password)) {
        $error = 'يرجى إدخال البريد الإلكتروني وكلمة المرور.';
    } else {
        // البحث عن المستخدم في قاعدة البيانات
        $sql = "SELECT user_id, name, email, password, role, status FROM Users WHERE email = ? AND role = 'admin' LIMIT 1";
        $stmt = executePreparedQuery($sql, 's', [$email]);
        
        if ($stmt && $stmt->execute()) {
            $result = $stmt->get_result();
            
            if ($result->num_rows === 1) {
                $user = $result->fetch_assoc();
                
                // التحقق من حالة المستخدم
                if ($user['status'] !== 'active') {
                    $error = 'حسابك غير نشط. يرجى الاتصال بالدعم الفني.';
                } else {
                    // التحقق من كلمة المرور
                    if (password_verify($password, $user['password'])) {
                        // تسجيل الدخول بنجاح
                        $_SESSION['user_id'] = $user['user_id'];
                        $_SESSION['user_name'] = $user['name'];
                        $_SESSION['user_email'] = $user['email'];
                        $_SESSION['user_role'] = $user['role'];
                        
                        // تحديث آخر تسجيل دخول
                        $update_sql = "UPDATE Users SET last_login = NOW() WHERE user_id = ?";
                        executePreparedQuery($update_sql, 'i', [$user['user_id']]);
                        
                        // إعادة التوجيه إلى لوحة التحكم
                        header('Location: index.php');
                        exit;
                    } else {
                        $error = 'كلمة المرور غير صحيحة.';
                    }
                }
            } else {
                $error = 'البريد الإلكتروني غير مسجل أو ليس لديك صلاحيات المدير.';
            }
            
            $stmt->close();
        } else {
            $error = 'حدث خطأ أثناء محاولة تسجيل الدخول. يرجى المحاولة مرة أخرى.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة التحكم</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6c63ff;
            --secondary-color: #4a42e8;
            --accent-color: #ff6b6b;
            --text-color: #333;
            --light-text: #666;
            --lighter-text: #999;
            --white: #fff;
            --light-bg: #f9f9f9;
            --border-color: #eee;
            --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Tajawal', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--light-bg);
            direction: rtl;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            width: 100%;
            max-width: 400px;
            padding: 40px;
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--shadow);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .login-form {
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
            font-size: 16px;
        }
        
        button {
            width: 100%;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 5px;
            padding: 12px;
            font-family: 'Tajawal', sans-serif;
            font-size: 16px;
            cursor: pointer;
            transition: var(--transition);
        }
        
        button:hover {
            background-color: var(--secondary-color);
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-link a {
            color: var(--primary-color);
            text-decoration: none;
            transition: var(--transition);
        }
        
        .back-link a:hover {
            color: var(--secondary-color);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>تسجيل الدخول</h1>
            <p>لوحة تحكم الموقع</p>
        </div>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <form class="login-form" method="post">
            <div class="form-group">
                <label for="email">البريد الإلكتروني</label>
                <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit">تسجيل الدخول</button>
        </form>
        
        <div class="back-link">
            <a href="../index.php"><i class="fas fa-arrow-right"></i> العودة إلى الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
