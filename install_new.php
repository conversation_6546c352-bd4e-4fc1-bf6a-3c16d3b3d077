<?php
/**
 * ملف التنصيب الجديد
 * يستخدم لتنصيب الموقع وإنشاء قاعدة البيانات
 */

// بدء جلسة لحفظ بيانات التثبيت
session_start();

// التحقق من وجود ملف الإعدادات
if (!file_exists('config.php')) {
    die('ملف الإعدادات (config.php) غير موجود. يرجى التأكد من وجود الملف في المجلد الرئيسي.');
}

// استيراد ملف الإعدادات
require_once 'config.php';

// التحقق من حالة التثبيت
if (defined('INSTALLED') && INSTALLED) {
    die('تم تثبيت الموقع بالفعل. إذا كنت ترغب في إعادة التثبيت، يرجى تعديل ملف الإعدادات (config.php) وتغيير قيمة INSTALLED إلى false.');
}

// تعريف المتغيرات
$step = isset($_SESSION['install_step']) ? (int)$_SESSION['install_step'] : 1;

// إذا تم تحديد الخطوة في الـ URL، استخدمها بدلاً من الجلسة
if (isset($_GET['step'])) {
    $step = (int)$_GET['step'];
    $_SESSION['install_step'] = $step;
}

$error = '';
$success = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['check_requirements'])) {
        // الانتقال إلى الخطوة 2
        $step = 2;
        $_SESSION['install_step'] = $step;
    } elseif (isset($_POST['db_settings'])) {
        // معالجة إعدادات قاعدة البيانات
        $db_host = trim($_POST['db_host']);
        $db_user = trim($_POST['db_user']);
        $db_pass = trim($_POST['db_pass']);
        $db_name = trim($_POST['db_name']);

        // حفظ بيانات قاعدة البيانات في الجلسة
        $_SESSION['db_host'] = $db_host;
        $_SESSION['db_user'] = $db_user;
        $_SESSION['db_pass'] = $db_pass;
        $_SESSION['db_name'] = $db_name;

        // التحقق من صحة البيانات
        if (empty($db_host) || empty($db_user) || empty($db_name)) {
            $error = 'يرجى ملء جميع حقول إعدادات قاعدة البيانات.';
        } else {
            try {
                // محاولة الاتصال بقاعدة البيانات
                $conn = new mysqli($db_host, $db_user, $db_pass);

                if ($conn->connect_error) {
                    $error = 'فشل الاتصال بقاعدة البيانات: ' . $conn->connect_error;
                } else {
                    // إنشاء قاعدة البيانات إذا لم تكن موجودة
                    $sql = "CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";

                    if ($conn->query($sql) === TRUE) {
                        // اختيار قاعدة البيانات
                        $conn->select_db($db_name);

                        // التحقق من وجود ملف قاعدة البيانات
                        if (!file_exists('database.sql')) {
                            $error = 'ملف قاعدة البيانات (database.sql) غير موجود.';
                        } else {
                            // استيراد ملف قاعدة البيانات
                            $sql_file = file_get_contents('database.sql');

                            // تقسيم الملف إلى استعلامات منفصلة
                            $queries = explode(';', $sql_file);

                            // تنفيذ كل استعلام
                            $error_occurred = false;
                            foreach ($queries as $query) {
                                $query = trim($query);

                                if (!empty($query)) {
                                    $conn->query($query);

                                    if ($conn->error) {
                                        $error = 'خطأ في تنفيذ الاستعلام: ' . $conn->error;
                                        $error_occurred = true;
                                        break;
                                    }
                                }
                            }

                            if (!$error_occurred) {
                                $success = 'تم الاتصال بقاعدة البيانات بنجاح وإنشاء الجداول.';
                                $step = 3;
                                $_SESSION['install_step'] = $step;
                            }
                        }
                    } else {
                        $error = 'فشل إنشاء قاعدة البيانات: ' . $conn->error;
                    }

                    $conn->close();
                }
            } catch (Exception $e) {
                $error = 'حدث خطأ: ' . $e->getMessage();
            }
        }
    } elseif (isset($_POST['site_settings'])) {
        // معالجة إعدادات الموقع
        $site_url = rtrim(trim($_POST['site_url']), '/');
        $admin_email = trim($_POST['admin_email']);
        $admin_name = trim($_POST['admin_name']);
        $admin_password = trim($_POST['admin_password']);
        $admin_password_confirm = trim($_POST['admin_password_confirm']);

        // حفظ بيانات الموقع في الجلسة
        $_SESSION['site_url'] = $site_url;
        $_SESSION['admin_email'] = $admin_email;
        $_SESSION['admin_name'] = $admin_name;

        // التحقق من صحة البيانات
        if (empty($site_url) || empty($admin_email) || empty($admin_name) || empty($admin_password)) {
            $error = 'يرجى ملء جميع حقول إعدادات الموقع.';
        } elseif ($admin_password !== $admin_password_confirm) {
            $error = 'كلمة المرور وتأكيدها غير متطابقين.';
        } else {
            // تحديث ملف الإعدادات
            if (!file_exists('config.php')) {
                $error = 'ملف الإعدادات (config.php) غير موجود.';
            } else {
                $config_file = file_get_contents('config.php');
                $config_file = str_replace("define('SITE_URL', '');", "define('SITE_URL', '$site_url');", $config_file);
                $config_file = str_replace("define('ADMIN_EMAIL', '');", "define('ADMIN_EMAIL', '$admin_email');", $config_file);
                $config_file = str_replace("define('MAIL_FROM', '');", "define('MAIL_FROM', '$admin_email');", $config_file);

                // تغيير حالة التثبيت إلى true
                $config_file = str_replace("define('INSTALLED', false);", "define('INSTALLED', true);", $config_file);

                if (!file_put_contents('config.php', $config_file)) {
                    $error = 'فشل في كتابة ملف الإعدادات. تأكد من أن الملف قابل للكتابة.';
                } else {
                    // تحديث بيانات المدير في قاعدة البيانات
                    try {
                        // استيراد ملف الاتصال بقاعدة البيانات
                        require_once 'db_connection.php';

                        // تشفير كلمة المرور
                        $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);

                        // الاتصال بقاعدة البيانات
                        $conn = connectDB();

                        if (!$conn) {
                            $error = 'فشل الاتصال بقاعدة البيانات.';
                        } else {
                            // تحديث بيانات المدير
                            $sql = "UPDATE Users SET name = ?, email = ?, password = ? WHERE role = 'admin' LIMIT 1";
                            $stmt = $conn->prepare($sql);

                            if (!$stmt) {
                                $error = 'فشل في إعداد الاستعلام: ' . $conn->error;
                            } else {
                                $stmt->bind_param('sss', $admin_name, $admin_email, $hashed_password);

                                if (!$stmt->execute()) {
                                    $error = 'فشل في تحديث بيانات المدير: ' . $stmt->error;
                                } else {
                                    $success = 'تم حفظ إعدادات الموقع وتحديث بيانات المدير بنجاح.';
                                    $step = 4;
                                    $_SESSION['install_step'] = $step;
                                }

                                $stmt->close();
                            }

                            $conn->close();
                        }
                    } catch (Exception $e) {
                        $error = 'حدث خطأ: ' . $e->getMessage();
                    }
                }
            }
        }
    }
}

// التحقق من متطلبات النظام
function checkRequirements() {
    $requirements = [
        'php_version' => [
            'name' => 'إصدار PHP',
            'required' => '7.4.0',
            'current' => PHP_VERSION,
            'status' => version_compare(PHP_VERSION, '7.4.0', '>=')
        ],
        'mysql_extension' => [
            'name' => 'امتداد MySQL',
            'required' => 'مثبت',
            'current' => extension_loaded('mysqli') ? 'مثبت' : 'غير مثبت',
            'status' => extension_loaded('mysqli')
        ],
        'gd_extension' => [
            'name' => 'امتداد GD',
            'required' => 'مثبت',
            'current' => extension_loaded('gd') ? 'مثبت' : 'غير مثبت',
            'status' => extension_loaded('gd')
        ],
        'config_writable' => [
            'name' => 'ملف الإعدادات قابل للكتابة',
            'required' => 'نعم',
            'current' => is_writable('config.php') ? 'نعم' : 'لا',
            'status' => is_writable('config.php')
        ]
    ];

    return $requirements;
}

// التحقق من استيفاء جميع المتطلبات
function allRequirementsMet($requirements) {
    foreach ($requirements as $requirement) {
        if (!$requirement['status']) {
            return false;
        }
    }

    return true;
}

// الحصول على متطلبات النظام
$requirements = checkRequirements();
$all_requirements_met = allRequirementsMet($requirements);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت موقع دعوات إلكترونية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6c63ff;
            --secondary-color: #4a42e8;
            --accent-color: #ff6b6b;
            --text-color: #333;
            --light-text: #666;
            --lighter-text: #999;
            --white: #fff;
            --light-bg: #f9f9f9;
            --border-color: #eee;
            --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--light-bg);
            direction: rtl;
        }

        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .install-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .install-header h1 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .install-content {
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--shadow);
            padding: 30px;
            margin-bottom: 30px;
        }

        .install-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            position: relative;
        }

        .install-steps::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--border-color);
            z-index: 1;
        }

        .step {
            position: relative;
            z-index: 2;
            text-align: center;
            width: 25%;
        }

        .step-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            background-color: var(--light-bg);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            margin: 0 auto 10px;
            color: var(--light-text);
            font-weight: bold;
        }

        .step.active .step-number {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--white);
        }

        .step.completed .step-number {
            background-color: #4CAF50;
            border-color: #4CAF50;
            color: var(--white);
        }

        .step-title {
            font-size: 14px;
            color: var(--light-text);
        }

        .step.active .step-title {
            color: var(--primary-color);
            font-weight: bold;
        }

        .step.completed .step-title {
            color: #4CAF50;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="text"],
        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
            font-size: 16px;
        }

        button {
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-family: 'Tajawal', sans-serif;
            font-size: 16px;
            cursor: pointer;
            transition: var(--transition);
        }

        button:hover {
            background-color: var(--secondary-color);
        }

        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table th,
        table td {
            padding: 10px;
            border: 1px solid var(--border-color);
        }

        table th {
            background-color: var(--light-bg);
            text-align: right;
        }

        .status-ok {
            color: #4CAF50;
        }

        .status-error {
            color: #f44336;
        }

        .install-footer {
            text-align: center;
            margin-top: 30px;
            color: var(--light-text);
        }

        .btn-container {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .btn-container.end {
            justify-content: flex-end;
        }

        .btn-container.center {
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-header">
            <h1>تثبيت موقع دعوات إلكترونية</h1>
            <p>اتبع الخطوات التالية لتثبيت الموقع</p>
        </div>

        <div class="install-content">
            <div class="install-steps">
                <div class="step <?php echo $step === 1 ? 'active' : ($step > 1 ? 'completed' : ''); ?>">
                    <div class="step-number"><?php echo $step > 1 ? '<i class="fas fa-check"></i>' : '1'; ?></div>
                    <div class="step-title">متطلبات النظام</div>
                </div>
                <div class="step <?php echo $step === 2 ? 'active' : ($step > 2 ? 'completed' : ''); ?>">
                    <div class="step-number"><?php echo $step > 2 ? '<i class="fas fa-check"></i>' : '2'; ?></div>
                    <div class="step-title">قاعدة البيانات</div>
                </div>
                <div class="step <?php echo $step === 3 ? 'active' : ($step > 3 ? 'completed' : ''); ?>">
                    <div class="step-number"><?php echo $step > 3 ? '<i class="fas fa-check"></i>' : '3'; ?></div>
                    <div class="step-title">إعدادات الموقع</div>
                </div>
                <div class="step <?php echo $step === 4 ? 'active' : ''; ?>">
                    <div class="step-number">4</div>
                    <div class="step-title">اكتمال التثبيت</div>
                </div>
            </div>

            <?php if (!empty($error)): ?>
                <div class="alert alert-danger">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <?php if ($step === 1): ?>
                <!-- الخطوة 1: متطلبات النظام -->
                <h2>متطلبات النظام</h2>
                <p>يرجى التأكد من توفر جميع متطلبات النظام التالية:</p>

                <table>
                    <tr>
                        <th>المتطلب</th>
                        <th>المطلوب</th>
                        <th>الحالي</th>
                        <th>الحالة</th>
                    </tr>
                    <?php foreach ($requirements as $requirement): ?>
                        <tr>
                            <td><?php echo $requirement['name']; ?></td>
                            <td><?php echo $requirement['required']; ?></td>
                            <td><?php echo $requirement['current']; ?></td>
                            <td class="<?php echo $requirement['status'] ? 'status-ok' : 'status-error'; ?>">
                                <?php echo $requirement['status'] ? '<i class="fas fa-check"></i>' : '<i class="fas fa-times"></i>'; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </table>

                <form method="post">
                    <div class="btn-container end">
                        <button type="submit" name="check_requirements" <?php echo $all_requirements_met ? '' : 'disabled'; ?>>
                            التالي <i class="fas fa-arrow-left"></i>
                        </button>
                    </div>
                </form>
            <?php elseif ($step === 2): ?>
                <!-- الخطوة 2: إعدادات قاعدة البيانات -->
                <h2>إعدادات قاعدة البيانات</h2>
                <p>يرجى إدخال معلومات قاعدة البيانات الخاصة بك:</p>

                <form method="post">
                    <div class="form-group">
                        <label for="db_host">مضيف قاعدة البيانات</label>
                        <input type="text" id="db_host" name="db_host" value="<?php echo isset($_SESSION['db_host']) ? htmlspecialchars($_SESSION['db_host']) : 'localhost'; ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="db_user">اسم مستخدم قاعدة البيانات</label>
                        <input type="text" id="db_user" name="db_user" value="<?php echo isset($_SESSION['db_user']) ? htmlspecialchars($_SESSION['db_user']) : ''; ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="db_pass">كلمة مرور قاعدة البيانات</label>
                        <input type="password" id="db_pass" name="db_pass" value="<?php echo isset($_SESSION['db_pass']) ? htmlspecialchars($_SESSION['db_pass']) : ''; ?>">
                    </div>

                    <div class="form-group">
                        <label for="db_name">اسم قاعدة البيانات</label>
                        <input type="text" id="db_name" name="db_name" value="<?php echo isset($_SESSION['db_name']) ? htmlspecialchars($_SESSION['db_name']) : 'e_invitations'; ?>" required>
                    </div>

                    <div class="btn-container">
                        <a href="?step=1" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> السابق
                        </a>
                        <button type="submit" name="db_settings">
                            التالي <i class="fas fa-arrow-left"></i>
                        </button>
                    </div>
                </form>
            <?php elseif ($step === 3): ?>
                <!-- الخطوة 3: إعدادات الموقع -->
                <h2>إعدادات الموقع</h2>
                <p>يرجى إدخال معلومات الموقع وحساب المدير:</p>

                <form method="post">
                    <div class="form-group">
                        <label for="site_url">رابط الموقع</label>
                        <input type="text" id="site_url" name="site_url" value="<?php echo isset($_SESSION['site_url']) ? htmlspecialchars($_SESSION['site_url']) : 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="admin_email">البريد الإلكتروني للمدير</label>
                        <input type="email" id="admin_email" name="admin_email" value="<?php echo isset($_SESSION['admin_email']) ? htmlspecialchars($_SESSION['admin_email']) : ''; ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="admin_name">اسم المدير</label>
                        <input type="text" id="admin_name" name="admin_name" value="<?php echo isset($_SESSION['admin_name']) ? htmlspecialchars($_SESSION['admin_name']) : 'أحمد محمد'; ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="admin_password">كلمة مرور المدير</label>
                        <input type="password" id="admin_password" name="admin_password" required>
                    </div>

                    <div class="form-group">
                        <label for="admin_password_confirm">تأكيد كلمة المرور</label>
                        <input type="password" id="admin_password_confirm" name="admin_password_confirm" required>
                    </div>

                    <div class="btn-container">
                        <a href="?step=2" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> السابق
                        </a>
                        <button type="submit" name="site_settings">
                            التالي <i class="fas fa-arrow-left"></i>
                        </button>
                    </div>
                </form>
            <?php elseif ($step === 4): ?>
                <!-- الخطوة 4: اكتمال التثبيت -->
                <h2>اكتمال التثبيت</h2>
                <p>تهانينا! تم تثبيت موقع دعوات إلكترونية بنجاح.</p>

                <div class="alert alert-success">
                    <p><i class="fas fa-check-circle"></i> تم تثبيت الموقع بنجاح!</p>
                </div>

                <p>يمكنك الآن:</p>
                <ul>
                    <li>الوصول إلى <a href="index.php">الصفحة الرئيسية</a></li>
                    <li>تسجيل الدخول إلى <a href="admin/login.php">لوحة التحكم</a></li>
                </ul>

                <div class="alert alert-danger">
                    <p><i class="fas fa-exclamation-triangle"></i> هام: يرجى حذف ملف التثبيت (install_new.php) من الخادم لأسباب أمنية.</p>
                </div>

                <div class="btn-container center">
                    <a href="index.php" class="btn btn-primary">
                        الذهاب إلى الصفحة الرئيسية <i class="fas fa-home"></i>
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <div class="install-footer">
            <p>دعوات إلكترونية &copy; <?php echo date('Y'); ?> جميع الحقوق محفوظة</p>
        </div>
    </div>
</body>
</html>


