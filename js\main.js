// Main JavaScript File

document.addEventListener('DOMContentLoaded', function() {
    // Mobile Menu Toggle
    const menuToggle = document.querySelector('.menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    const authButtons = document.querySelector('.auth-buttons');

    if (menuToggle) {
        menuToggle.addEventListener('click', function() {
            navLinks.classList.toggle('active');
            authButtons.classList.toggle('active');
            menuToggle.classList.toggle('active');
        });
    }

    // Smooth Scrolling for Anchor Links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            // Close mobile menu if open
            if (navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                authButtons.classList.remove('active');
                menuToggle.classList.remove('active');
            }

            // Scroll to the target section
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80, // Adjust for navbar height
                    behavior: 'smooth'
                });
            }
        });
    });

    // Sticky Navbar
    const navbar = document.querySelector('.navbar');
    const heroSection = document.querySelector('.hero');

    if (navbar && heroSection) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('sticky');
            } else {
                navbar.classList.remove('sticky');
            }
        });
    }

    // Add active class to nav links based on scroll position
    const sections = document.querySelectorAll('section[id]');
    
    function highlightNavLink() {
        const scrollY = window.pageYOffset;
        
        sections.forEach(section => {
            const sectionHeight = section.offsetHeight;
            const sectionTop = section.offsetTop - 100;
            const sectionId = section.getAttribute('id');
            
            if (scrollY > sectionTop && scrollY <= sectionTop + sectionHeight) {
                document.querySelector('.nav-links a[href*=' + sectionId + ']')?.classList.add('active');
            } else {
                document.querySelector('.nav-links a[href*=' + sectionId + ']')?.classList.remove('active');
            }
        });
    }

    window.addEventListener('scroll', highlightNavLink);

    // Add CSS for mobile menu when active
    const style = document.createElement('style');
    style.textContent = `
        @media screen and (max-width: 768px) {
            .nav-links.active, .auth-buttons.active {
                display: block;
                position: absolute;
                top: 70px;
                left: 0;
                width: 100%;
                background-color: white;
                padding: 20px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                z-index: 999;
            }
            
            .nav-links.active ul {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-links.active li {
                margin: 15px 0;
            }
            
            .auth-buttons.active {
                top: calc(70px + 200px);
                text-align: center;
                padding-bottom: 30px;
            }
            
            .menu-toggle.active i:before {
                content: "\\f00d";
            }
            
            .navbar.sticky {
                padding: 10px 0;
                background-color: white;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            }
        }
    `;
    document.head.appendChild(style);
});
