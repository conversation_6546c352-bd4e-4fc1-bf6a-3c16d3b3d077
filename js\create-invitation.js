/**
 * Create Invitation Module
 * This module handles the invitation creation process
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const nextBtn = document.getElementById('next-btn');
    const prevBtn = document.getElementById('prev-btn');
    const steps = document.querySelectorAll('.step');
    const stepContents = document.querySelectorAll('.step-content');

    // Current step
    let currentStep = 1;

    // Initialize QR code and barcode containers if we're on step 3
    let qrCodeContainer = null;
    let barcodeContainer = null;
    let templateContainer = null;

    // Initialize the form
    initForm();

    /**
     * Initialize the form
     */
    function initForm() {
        // Add event listeners to navigation buttons
        if (nextBtn) {
            nextBtn.addEventListener('click', goToNextStep);
        }

        if (prevBtn) {
            prevBtn.addEventListener('click', goToPrevStep);
        }

        // Add event listeners to template cards
        const templateCards = document.querySelectorAll('.template-card');
        templateCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove selected class from all cards
                templateCards.forEach(c => c.classList.remove('selected'));

                // Add selected class to clicked card
                this.classList.add('selected');

                // Check the radio button
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                }
            });
        });

        // Add event listeners to preview buttons
        const previewButtons = document.querySelectorAll('.preview-btn');
        previewButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation(); // Prevent triggering the card click

                const templateCard = this.closest('.template-card');
                const templateId = templateCard.querySelector('input[type="radio"]').value;

                // Open the template preview in a new window/tab
                window.open(`template-preview.html?template=${templateId}`, '_blank');
            });
        });
    }

    /**
     * Go to the next step
     */
    function goToNextStep() {
        // Validate current step
        if (!validateStep(currentStep)) {
            return;
        }

        // Hide current step
        document.getElementById(`step-${currentStep}`).classList.remove('active');

        // Increment current step
        currentStep++;

        // Show next step
        document.getElementById(`step-${currentStep}`).classList.add('active');

        // Update step indicators
        updateStepIndicators();

        // Update navigation buttons
        updateNavigationButtons();

        // Initialize step-specific functionality
        initializeStepFunctionality(currentStep);
    }

    /**
     * Go to the previous step
     */
    function goToPrevStep() {
        // Hide current step
        document.getElementById(`step-${currentStep}`).classList.remove('active');

        // Decrement current step
        currentStep--;

        // Show previous step
        document.getElementById(`step-${currentStep}`).classList.add('active');

        // Update step indicators
        updateStepIndicators();

        // Update navigation buttons
        updateNavigationButtons();

        // Initialize step-specific functionality
        initializeStepFunctionality(currentStep);
    }

    /**
     * Update step indicators
     */
    function updateStepIndicators() {
        steps.forEach(step => {
            const stepNumber = parseInt(step.getAttribute('data-step'));

            if (stepNumber === currentStep) {
                step.classList.add('active');
            } else if (stepNumber < currentStep) {
                step.classList.add('completed');
                step.classList.remove('active');
            } else {
                step.classList.remove('active', 'completed');
            }
        });
    }

    /**
     * Update navigation buttons
     */
    function updateNavigationButtons() {
        // Enable/disable previous button
        if (currentStep === 1) {
            prevBtn.disabled = true;
        } else {
            prevBtn.disabled = false;
        }

        // Update next button text
        if (currentStep === 5) {
            nextBtn.textContent = 'إنشاء الدعوة';
        } else {
            nextBtn.textContent = 'التالي';
        }
    }

    /**
     * Validate the current step
     * @param {number} step - The step to validate
     * @returns {boolean} - Whether the step is valid
     */
    function validateStep(step) {
        switch (step) {
            case 1:
                // Validate template selection
                const selectedTemplate = document.querySelector('input[name="template"]:checked');
                if (!selectedTemplate) {
                    alert('الرجاء اختيار قالب');
                    return false;
                }
                return true;

            case 2:
                // Validate event details
                // This would be implemented when step 2 is added to the HTML
                return true;

            case 3:
                // Validate design customization
                // This would be implemented when step 3 is added to the HTML
                return true;

            case 4:
                // Validate guest list
                // This would be implemented when step 4 is added to the HTML
                return true;

            case 5:
                // Validate preview and publish
                // This would be implemented when step 5 is added to the HTML
                return true;

            default:
                return true;
        }
    }

    /**
     * Initialize step-specific functionality
     * @param {number} step - The step to initialize
     */
    function initializeStepFunctionality(step) {
        switch (step) {
            case 1:
                // Template selection step
                // Nothing special to initialize
                break;

            case 2:
                // Event details step
                initEventDetailsStep();
                break;

            case 3:
                // Design customization step
                initDesignCustomizationStep();
                break;

            case 4:
                // Guest list step
                initGuestListStep();
                break;

            case 5:
                // Preview and publish step
                initPreviewAndPublishStep();
                break;
        }
    }

    /**
     * Initialize the event details step
     */
    function initEventDetailsStep() {
        // This would be implemented when step 2 is added to the HTML
        console.log('Initializing event details step');
    }

    /**
     * Initialize the design customization step
     */
    function initDesignCustomizationStep() {
        console.log('Initializing design customization step');

        // Get the template container
        templateContainer = document.querySelector('.template-customization-preview');

        if (!templateContainer) return;

        // Get the selected template
        const selectedTemplate = document.querySelector('input[name="template"]:checked').value;

        // Load the template preview
        templateContainer.innerHTML = `<div class="template-loading">جاري تحميل القالب...</div>`;

        // In a real application, you would load the actual template
        // For now, we'll just simulate it
        setTimeout(() => {
            templateContainer.innerHTML = `
                <div class="template-preview-container">
                    <img src="images/templates/${selectedTemplate}.jpg" alt="معاينة القالب">
                </div>
            `;

            // Initialize QR code functionality
            initQRCodeFunctionality();

            // Initialize barcode functionality
            initBarcodeFunctionality();

            // Initialize image upload functionality
            initImageUploadFunctionality();
        }, 500);
    }

    /**
     * Initialize QR code functionality
     */
    function initQRCodeFunctionality() {
        // Create QR code container
        qrCodeContainer = document.createElement('div');
        qrCodeContainer.className = 'qr-code-container';
        templateContainer.appendChild(qrCodeContainer);

        // Generate a sample QR code
        if (window.QRCode && window.QRCodeGenerator) {
            QRCodeGenerator.generate('https://example.com/invitation/123', qrCodeContainer, {
                width: 100,
                height: 100
            });

            // Position the QR code
            QRCodeGenerator.positionOnTemplate(qrCodeContainer, { x: 20, y: 20 }, templateContainer);
        }

        // Add QR code customization controls
        const qrCodeControls = document.querySelector('.qr-code-controls');

        if (qrCodeControls) {
            // Create form groups for better organization

            // QR Code value input
            const valueGroup = document.createElement('div');
            valueGroup.className = 'form-group';

            const valueLabel = document.createElement('label');
            valueLabel.htmlFor = 'qr-value';
            valueLabel.textContent = 'رابط الدعوة:';

            const valueInput = document.createElement('input');
            valueInput.type = 'text';
            valueInput.id = 'qr-value';
            valueInput.value = 'https://example.com/invitation/123';
            valueInput.placeholder = 'أدخل رابط الدعوة';

            valueGroup.appendChild(valueLabel);
            valueGroup.appendChild(valueInput);
            qrCodeControls.appendChild(valueGroup);

            // Color picker for QR code
            const colorGroup = document.createElement('div');
            colorGroup.className = 'form-group';

            const colorLabel = document.createElement('label');
            colorLabel.htmlFor = 'qr-color';
            colorLabel.textContent = 'لون الكود:';

            const colorPicker = document.createElement('input');
            colorPicker.type = 'color';
            colorPicker.value = '#000000';
            colorPicker.id = 'qr-color';

            colorGroup.appendChild(colorLabel);
            colorGroup.appendChild(colorPicker);
            qrCodeControls.appendChild(colorGroup);

            // Background color picker for QR code
            const bgColorGroup = document.createElement('div');
            bgColorGroup.className = 'form-group';

            const bgColorLabel = document.createElement('label');
            bgColorLabel.htmlFor = 'qr-bg-color';
            bgColorLabel.textContent = 'لون الخلفية:';

            const bgColorPicker = document.createElement('input');
            bgColorPicker.type = 'color';
            bgColorPicker.value = '#FFFFFF';
            bgColorPicker.id = 'qr-bg-color';

            bgColorGroup.appendChild(bgColorLabel);
            bgColorGroup.appendChild(bgColorPicker);
            qrCodeControls.appendChild(bgColorGroup);

            // Size slider for QR code
            const sizeGroup = document.createElement('div');
            sizeGroup.className = 'form-group';

            const sizeLabel = document.createElement('label');
            sizeLabel.htmlFor = 'qr-size';
            sizeLabel.textContent = 'حجم الكود:';

            const sizeSlider = document.createElement('input');
            sizeSlider.type = 'range';
            sizeSlider.min = '50';
            sizeSlider.max = '200';
            sizeSlider.value = '100';
            sizeSlider.id = 'qr-size';

            sizeGroup.appendChild(sizeLabel);
            sizeGroup.appendChild(sizeSlider);
            qrCodeControls.appendChild(sizeGroup);

            // Error correction level
            const correctionGroup = document.createElement('div');
            correctionGroup.className = 'form-group';

            const correctionLabel = document.createElement('label');
            correctionLabel.htmlFor = 'qr-correction';
            correctionLabel.textContent = 'مستوى تصحيح الخطأ:';

            const correctionSelect = document.createElement('select');
            correctionSelect.id = 'qr-correction';

            const correctionLevels = [
                { value: 'L', text: 'منخفض (7%)' },
                { value: 'M', text: 'متوسط (15%)' },
                { value: 'Q', text: 'جيد (25%)' },
                { value: 'H', text: 'عالي (30%)' }
            ];

            correctionLevels.forEach(level => {
                const option = document.createElement('option');
                option.value = level.value;
                option.textContent = level.text;
                if (level.value === 'H') option.selected = true;
                correctionSelect.appendChild(option);
            });

            correctionGroup.appendChild(correctionLabel);
            correctionGroup.appendChild(correctionSelect);
            qrCodeControls.appendChild(correctionGroup);

            // Toggle visibility
            const toggleGroup = document.createElement('div');
            toggleGroup.className = 'form-group';

            const toggleLabel = document.createElement('label');
            toggleLabel.htmlFor = 'qr-toggle';

            const toggleCheckbox = document.createElement('input');
            toggleCheckbox.type = 'checkbox';
            toggleCheckbox.id = 'qr-toggle';
            toggleCheckbox.checked = true;

            toggleLabel.appendChild(toggleCheckbox);
            toggleLabel.appendChild(document.createTextNode(' إظهار كود QR'));

            toggleGroup.appendChild(toggleLabel);
            qrCodeControls.appendChild(toggleGroup);

            // Update button
            const updateBtn = document.createElement('button');
            updateBtn.className = 'btn btn-primary btn-sm';
            updateBtn.textContent = 'تحديث الكود';
            qrCodeControls.appendChild(updateBtn);

            // Function to update QR code
            const updateQRCode = function() {
                if (window.QRCode && window.QRCodeGenerator) {
                    // Get the current position
                    const position = QRCodeGenerator.getPosition(qrCodeContainer);

                    // Get correction level
                    let correctLevel;
                    switch(correctionSelect.value) {
                        case 'L': correctLevel = QRCode.CorrectLevel.L; break;
                        case 'M': correctLevel = QRCode.CorrectLevel.M; break;
                        case 'Q': correctLevel = QRCode.CorrectLevel.Q; break;
                        case 'H': correctLevel = QRCode.CorrectLevel.H; break;
                        default: correctLevel = QRCode.CorrectLevel.H;
                    }

                    // Regenerate the QR code with the new options
                    QRCodeGenerator.generate(valueInput.value, qrCodeContainer, {
                        width: parseInt(sizeSlider.value),
                        height: parseInt(sizeSlider.value),
                        colorDark: colorPicker.value,
                        colorLight: bgColorPicker.value,
                        correctLevel: correctLevel
                    });

                    // Reposition the QR code
                    QRCodeGenerator.positionOnTemplate(qrCodeContainer, position, templateContainer);
                }
            };

            // Add event listeners
            updateBtn.addEventListener('click', updateQRCode);

            // Toggle visibility
            toggleCheckbox.addEventListener('change', function() {
                qrCodeContainer.style.display = this.checked ? 'block' : 'none';
            });
        }
    }

    /**
     * Initialize barcode functionality
     */
    function initBarcodeFunctionality() {
        // Create barcode container
        barcodeContainer = document.createElement('div');
        barcodeContainer.className = 'barcode-container';
        templateContainer.appendChild(barcodeContainer);

        // Generate a sample barcode
        if (window.JsBarcode && window.BarcodeGenerator) {
            BarcodeGenerator.generate('123456789012', barcodeContainer, {
                format: 'CODE128',
                width: 2,
                height: 100,
                displayValue: true,
                lineColor: '#000000'
            });

            // Position the barcode
            BarcodeGenerator.positionOnTemplate(barcodeContainer, { x: 20, y: 150 }, templateContainer);
        }

        // Add barcode customization controls
        const barcodeControls = document.querySelector('.barcode-controls');

        if (barcodeControls && window.JsBarcode && window.BarcodeGenerator) {
            // Get the control elements
            const formatSelect = document.getElementById('barcode-format');
            const colorPicker = document.getElementById('barcode-color');
            const widthSlider = document.getElementById('barcode-width');
            const heightSlider = document.getElementById('barcode-height');
            const displayValueCheckbox = document.getElementById('barcode-display-value');

            // Add barcode value input
            const valueInputGroup = document.createElement('div');
            valueInputGroup.className = 'form-group';

            const valueLabel = document.createElement('label');
            valueLabel.htmlFor = 'barcode-value';
            valueLabel.textContent = 'قيمة الباركود';

            const valueInput = document.createElement('input');
            valueInput.type = 'text';
            valueInput.id = 'barcode-value';
            valueInput.value = '123456789012';
            valueInput.placeholder = 'أدخل قيمة الباركود';

            valueInputGroup.appendChild(valueLabel);
            valueInputGroup.appendChild(valueInput);

            // Insert at the beginning of controls
            barcodeControls.insertBefore(valueInputGroup, barcodeControls.firstChild);

            // Function to update the barcode
            const updateBarcode = function() {
                // Get the current position
                const position = BarcodeGenerator.getPosition(barcodeContainer);

                // Get the barcode value
                const barcodeValue = valueInput.value || '123456789012';

                // Validate barcode value based on format
                let isValid = true;
                let errorMessage = '';

                switch(formatSelect.value) {
                    case 'EAN13':
                        if (!/^\d{13}$/.test(barcodeValue)) {
                            isValid = false;
                            errorMessage = 'يجب أن يتكون EAN13 من 13 رقم';
                        }
                        break;
                    case 'EAN8':
                        if (!/^\d{8}$/.test(barcodeValue)) {
                            isValid = false;
                            errorMessage = 'يجب أن يتكون EAN8 من 8 أرقام';
                        }
                        break;
                    case 'UPC':
                        if (!/^\d{12}$/.test(barcodeValue)) {
                            isValid = false;
                            errorMessage = 'يجب أن يتكون UPC من 12 رقم';
                        }
                        break;
                    case 'CODE39':
                        if (!/^[A-Z0-9\-\.\ \$\/\+\%]+$/.test(barcodeValue)) {
                            isValid = false;
                            errorMessage = 'CODE39 يدعم فقط الأحرف الكبيرة والأرقام وبعض الرموز الخاصة';
                        }
                        break;
                }

                if (!isValid) {
                    alert(errorMessage);
                    return;
                }

                try {
                    // Regenerate the barcode with the new options
                    BarcodeGenerator.generate(barcodeValue, barcodeContainer, {
                        format: formatSelect.value,
                        width: parseFloat(widthSlider.value),
                        height: parseInt(heightSlider.value),
                        displayValue: displayValueCheckbox.checked,
                        lineColor: colorPicker.value
                    });

                    // Reposition the barcode
                    BarcodeGenerator.positionOnTemplate(barcodeContainer, position, templateContainer);
                } catch (error) {
                    alert('حدث خطأ أثناء إنشاء الباركود: ' + error.message);
                }
            };

            // Add event listeners
            valueInput.addEventListener('change', updateBarcode);
            formatSelect.addEventListener('change', updateBarcode);
            colorPicker.addEventListener('change', updateBarcode);
            widthSlider.addEventListener('change', updateBarcode);
            heightSlider.addEventListener('change', updateBarcode);
            displayValueCheckbox.addEventListener('change', updateBarcode);

            // Add a toggle button to show/hide barcode
            const toggleGroup = document.createElement('div');
            toggleGroup.className = 'form-group';

            const toggleLabel = document.createElement('label');
            toggleLabel.htmlFor = 'barcode-toggle';

            const toggleCheckbox = document.createElement('input');
            toggleCheckbox.type = 'checkbox';
            toggleCheckbox.id = 'barcode-toggle';
            toggleCheckbox.checked = true;

            toggleLabel.appendChild(toggleCheckbox);
            toggleLabel.appendChild(document.createTextNode(' إظهار الباركود'));

            toggleGroup.appendChild(toggleLabel);
            barcodeControls.appendChild(toggleGroup);

            // Add toggle event listener
            toggleCheckbox.addEventListener('change', function() {
                barcodeContainer.style.display = this.checked ? 'block' : 'none';
            });
        }
    }

    /**
     * Initialize image upload functionality
     */
    function initImageUploadFunctionality() {
        // Get the image upload elements
        const fileInput = document.getElementById('invitation-image');
        const previewContainer = document.querySelector('.image-preview-container');

        if (!fileInput || !previewContainer || !window.ImageUploader) return;

        // Initialize the image uploader
        ImageUploader.init(fileInput, previewContainer, {
            onFileSelected: function(file) {
                console.log('File selected:', file.name);
            },
            onError: function(error) {
                alert(error);
            }
        });

        // Add placeholder text
        const placeholder = document.createElement('div');
        placeholder.classList.add('upload-placeholder');
        placeholder.innerHTML = `
            <i class="fas fa-cloud-upload-alt"></i>
            <p>اسحب الصورة هنا أو انقر للاختيار</p>
        `;

        previewContainer.appendChild(placeholder);

        // Make the preview container clickable
        previewContainer.addEventListener('click', function() {
            fileInput.click();
        });
    }

    /**
     * Initialize the guest list step
     */
    function initGuestListStep() {
        // This would be implemented when step 4 is added to the HTML
        console.log('Initializing guest list step');
    }

    /**
     * Initialize the preview and publish step
     */
    function initPreviewAndPublishStep() {
        // This would be implemented when step 5 is added to the HTML
        console.log('Initializing preview and publish step');
    }
});
