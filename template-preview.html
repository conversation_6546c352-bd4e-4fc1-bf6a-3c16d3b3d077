<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة القالب | دعوات إلكترونية</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Tajawal is a good Arabic font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="logo">
                    <a href="index.html">
                        <div class="text-logo">دعوات إلكترونية</div>
                    </a>
                </div>
                <div class="nav-links">
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="index.html#features">المميزات</a></li>
                        <li><a href="index.html#how-it-works">كيف يعمل</a></li>
                        <li><a href="index.html#testimonials">العملاء</a></li>
                        <li><a href="index.html#pricing">الباقات والأسعار</a></li>
                        <li><a href="index.html#contact">تواصل معنا</a></li>
                    </ul>
                </div>
                <div class="auth-buttons">
                    <a href="login.html" class="btn btn-login">تسجيل الدخول</a>
                    <a href="admin/login.html" class="btn btn-secondary">لوحة التحكم</a>
                </div>
                <div class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Template Preview Section -->
    <section class="template-preview-section">
        <div class="container">
            <div class="template-preview-container">
                <div class="template-sidebar">
                    <div class="template-info">
                        <h2>قالب زفاف أنيق</h2>
                        <div class="template-meta">
                            <span class="template-category">زفاف</span>
                            <span class="template-style">أنيق</span>
                        </div>
                        <div class="template-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                            </div>
                            <span>4.5 (120 تقييم)</span>
                        </div>
                        <p class="template-description">
                            قالب زفاف أنيق بتصميم عصري وألوان راقية. مناسب لحفلات الزفاف الفاخرة والمميزة. يمكن تخصيص جميع العناصر بما يناسب ذوقك.
                        </p>
                    </div>

                    <div class="template-features">
                        <h3>المميزات</h3>
                        <ul>
                            <li><i class="fas fa-check-circle"></i> تصميم متجاوب لجميع الأجهزة</li>
                            <li><i class="fas fa-check-circle"></i> إمكانية تخصيص الألوان</li>
                            <li><i class="fas fa-check-circle"></i> دعم الصور عالية الدقة</li>
                            <li><i class="fas fa-check-circle"></i> خريطة تفاعلية لموقع الحفل</li>
                            <li><i class="fas fa-check-circle"></i> تأكيد الحضور المباشر</li>
                            <li><i class="fas fa-check-circle"></i> مشاركة سهلة عبر وسائل التواصل</li>
                            <li><i class="fas fa-check-circle"></i> دعم الباركود وQR Code</li>
                        </ul>
                    </div>

                    <div class="template-customization">
                        <h3>تجربة سريعة للتخصيص</h3>
                        <div class="customization-options">
                            <div class="customization-group">
                                <label>اللون الرئيسي</label>
                                <input type="color" id="primary-color-preview" value="#6c63ff">
                            </div>
                            <div class="customization-group">
                                <label>اللون الثانوي</label>
                                <input type="color" id="secondary-color-preview" value="#ff6b6b">
                            </div>
                            <div class="customization-group">
                                <label>نمط الخط</label>
                                <select id="font-family-preview">
                                    <option value="Tajawal" selected>Tajawal</option>
                                    <option value="Cairo">Cairo</option>
                                    <option value="Amiri">Amiri</option>
                                    <option value="Changa">Changa</option>
                                </select>
                            </div>
                            <button class="btn btn-primary btn-sm" id="apply-preview-btn">تطبيق</button>
                        </div>
                    </div>

                    <div class="template-actions">
                        <button class="btn btn-primary btn-block" id="use-template-btn">استخدام هذا القالب</button>
                        <button class="btn btn-outline btn-block" id="preview-mobile-btn">
                            <i class="fas fa-mobile-alt"></i>
                            معاينة على الجوال
                        </button>
                    </div>

                    <div class="template-more">
                        <h3>قوالب مشابهة</h3>
                        <div class="similar-templates">
                            <div class="similar-template">
                                <img src="images/templates/template2-thumb.jpg" alt="قالب زفاف كلاسيكي">
                                <div class="similar-template-info">
                                    <h4>قالب زفاف كلاسيكي</h4>
                                    <div class="stars small">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="similar-template">
                                <img src="images/templates/template3-thumb.jpg" alt="قالب زفاف ذهبي">
                                <div class="similar-template-info">
                                    <h4>قالب زفاف ذهبي</h4>
                                    <div class="stars small">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="similar-template">
                                <img src="images/templates/template4-thumb.jpg" alt="قالب زفاف بسيط">
                                <div class="similar-template-info">
                                    <h4>قالب زفاف بسيط</h4>
                                    <div class="stars small">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                        <i class="far fa-star"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="template-preview-frame">
                    <div class="preview-controls">
                        <div class="device-selector">
                            <button class="device-btn active" data-device="desktop">
                                <i class="fas fa-desktop"></i>
                            </button>
                            <button class="device-btn" data-device="tablet">
                                <i class="fas fa-tablet-alt"></i>
                            </button>
                            <button class="device-btn" data-device="mobile">
                                <i class="fas fa-mobile-alt"></i>
                            </button>
                        </div>
                        <button class="btn btn-outline btn-sm" id="fullscreen-btn">
                            <i class="fas fa-expand"></i>
                            عرض بملء الشاشة
                        </button>
                    </div>

                    <div class="preview-frame-container desktop">
                        <div class="preview-frame">
                            <iframe src="templates/wedding-elegant/index.html" frameborder="0"></iframe>
                            <div class="iframe-error" style="display: none;">
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    <h3>حدث خطأ أثناء تحميل القالب</h3>
                                    <p>يرجى المحاولة مرة أخرى أو اختيار قالب آخر</p>
                                    <button class="btn btn-primary reload-btn">إعادة تحميل</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <div class="text-logo">دعوات إلكترونية</div>
                    <p>شركة للاتصالات وتقنية المعلومات</p>
                    <p>الرياض - المملكة العربية السعودية</p>
                </div>
                <div class="footer-links">
                    <div class="footer-links-column">
                        <h3>روابط سريعة</h3>
                        <ul>
                            <li><a href="index.html">الرئيسية</a></li>
                            <li><a href="index.html#features">المميزات</a></li>
                            <li><a href="index.html#how-it-works">كيف يعمل</a></li>
                            <li><a href="index.html#testimonials">العملاء</a></li>
                            <li><a href="index.html#pricing">الباقات والأسعار</a></li>
                            <li><a href="index.html#contact">تواصل معنا</a></li>
                        </ul>
                    </div>
                    <div class="footer-links-column">
                        <h3>الشروط والأحكام</h3>
                        <ul>
                            <li><a href="terms.html">شروط الاستخدام</a></li>
                            <li><a href="privacy.html">سياسة الخصوصية</a></li>
                            <li><a href="faq.html">الأسئلة الشائعة</a></li>
                        </ul>
                    </div>
                </div>
                <div class="footer-social">
                    <h3>تواصل معنا</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-whatsapp"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© 2025 جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/template-preview.js"></script>
    <style>
        /* Template Preview Styles */
        .template-preview-section {
            padding: 60px 0;
            background-color: #f8f9fa;
        }

        .template-preview-container {
            display: flex;
            gap: 30px;
        }

        .template-sidebar {
            width: 350px;
            flex-shrink: 0;
        }

        .template-info {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 20px;
        }

        .template-info h2 {
            font-size: 1.8rem;
            margin-bottom: 15px;
            color: #333;
        }

        .template-meta {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .template-category,
        .template-style {
            background-color: #f0f0f0;
            color: #666;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .template-rating {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .stars {
            color: #ffc107;
        }

        .stars.small {
            font-size: 0.8rem;
        }

        .template-description {
            color: #666;
            line-height: 1.6;
        }

        .template-features {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 20px;
        }

        .template-features h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #333;
        }

        .template-features ul {
            list-style: none;
            padding: 0;
        }

        .template-features li {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 12px;
            color: #555;
        }

        .template-features li i {
            color: #4cd964;
        }

        .template-customization {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 20px;
        }

        .template-customization h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #333;
        }

        .customization-options {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .customization-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .customization-group label {
            font-size: 0.9rem;
            color: #666;
        }

        .customization-group input[type="color"] {
            width: 100%;
            height: 40px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            cursor: pointer;
        }

        .customization-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
            color: #333;
        }

        .template-actions {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 20px;
        }

        .template-actions .btn {
            margin-bottom: 10px;
        }

        .template-more {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 25px;
        }

        .template-more h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #333;
        }

        .similar-templates {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .similar-template {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px;
            border-radius: 8px;
            transition: background-color 0.3s;
            cursor: pointer;
        }

        .similar-template:hover {
            background-color: #f0f0f0;
        }

        .similar-template img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            border-radius: 8px;
        }

        .similar-template-info h4 {
            font-size: 1rem;
            margin-bottom: 5px;
            color: #333;
        }

        .template-preview-frame {
            flex: 1;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
        }

        .preview-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .device-selector {
            display: flex;
            gap: 10px;
            background-color: #f0f0f0;
            border-radius: 8px;
            padding: 5px;
        }

        .device-btn {
            border: none;
            background: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            color: #666;
            transition: all 0.3s;
        }

        .device-btn.active {
            background-color: white;
            color: #333;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .btn-sm {
            padding: 8px 12px;
            font-size: 0.9rem;
        }

        .preview-frame-container {
            height: 600px;
            overflow: hidden;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            transition: all 0.3s;
        }

        .preview-frame-container.desktop {
            width: 100%;
        }

        .preview-frame-container.tablet {
            width: 768px;
            margin: 0 auto;
        }

        .preview-frame-container.mobile {
            width: 375px;
            margin: 0 auto;
        }

        .preview-frame {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .iframe-error {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .error-message {
            text-align: center;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            max-width: 80%;
        }

        .error-message i {
            font-size: 3rem;
            color: #ff3b30;
            margin-bottom: 15px;
        }

        .error-message h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #333;
        }

        .error-message p {
            color: #666;
            margin-bottom: 20px;
        }

        .reload-btn {
            padding: 10px 20px;
        }

        .preview-frame {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .preview-frame iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Responsive styles */
        @media (max-width: 992px) {
            .template-preview-container {
                flex-direction: column;
            }

            .template-sidebar {
                width: 100%;
            }
        }
    </style>
</body>
</html>
