/**
 * Template Editor Module
 * This module handles template creation and editing functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const templateForm = document.getElementById('template-form');
    const templatePreview = document.querySelector('.template-editor-preview');
    const templateNameInput = document.getElementById('template-name');
    const templateCategorySelect = document.getElementById('template-category');
    const templateStyleSelect = document.getElementById('template-style');
    const templateColorSelect = document.getElementById('template-color');
    const templateDescriptionInput = document.getElementById('template-description');
    const templateHtmlEditor = document.getElementById('template-html');
    const templateCssEditor = document.getElementById('template-css');
    const templateJsEditor = document.getElementById('template-js');
    const saveTemplateBtn = document.getElementById('save-template-btn');
    const previewTemplateBtn = document.getElementById('preview-template-btn');
    
    // Initialize the template editor
    initTemplateEditor();
    
    /**
     * Initialize the template editor
     */
    function initTemplateEditor() {
        // Add event listeners
        if (previewTemplateBtn) {
            previewTemplateBtn.addEventListener('click', previewTemplate);
        }
        
        if (saveTemplateBtn) {
            saveTemplateBtn.addEventListener('click', saveTemplate);
        }
        
        // Initialize code editors if available
        initCodeEditors();
    }
    
    /**
     * Initialize code editors
     */
    function initCodeEditors() {
        // Check if CodeMirror is available
        if (window.CodeMirror) {
            // Initialize HTML editor
            if (templateHtmlEditor) {
                window.htmlEditor = CodeMirror.fromTextArea(templateHtmlEditor, {
                    mode: 'htmlmixed',
                    lineNumbers: true,
                    theme: 'monokai',
                    lineWrapping: true,
                    autoCloseTags: true
                });
            }
            
            // Initialize CSS editor
            if (templateCssEditor) {
                window.cssEditor = CodeMirror.fromTextArea(templateCssEditor, {
                    mode: 'css',
                    lineNumbers: true,
                    theme: 'monokai',
                    lineWrapping: true
                });
            }
            
            // Initialize JS editor
            if (templateJsEditor) {
                window.jsEditor = CodeMirror.fromTextArea(templateJsEditor, {
                    mode: 'javascript',
                    lineNumbers: true,
                    theme: 'monokai',
                    lineWrapping: true
                });
            }
        }
    }
    
    /**
     * Preview the template
     */
    function previewTemplate(e) {
        e.preventDefault();
        
        // Get the template content
        const html = window.htmlEditor ? window.htmlEditor.getValue() : templateHtmlEditor.value;
        const css = window.cssEditor ? window.cssEditor.getValue() : templateCssEditor.value;
        const js = window.jsEditor ? window.jsEditor.getValue() : templateJsEditor.value;
        
        // Create the preview content
        const previewContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>${css}</style>
            </head>
            <body>
                ${html}
                <script>${js}</script>
            </body>
            </html>
        `;
        
        // Update the preview iframe
        const previewFrame = document.querySelector('.template-preview-frame iframe');
        
        if (previewFrame) {
            const frameDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;
            frameDoc.open();
            frameDoc.write(previewContent);
            frameDoc.close();
        }
    }
    
    /**
     * Save the template
     */
    function saveTemplate(e) {
        e.preventDefault();
        
        // Validate the form
        if (!validateTemplateForm()) {
            return;
        }
        
        // Get the template data
        const templateData = {
            name: templateNameInput.value,
            category: templateCategorySelect.value,
            style: templateStyleSelect.value,
            color: templateColorSelect.value,
            description: templateDescriptionInput.value,
            html: window.htmlEditor ? window.htmlEditor.getValue() : templateHtmlEditor.value,
            css: window.cssEditor ? window.cssEditor.getValue() : templateCssEditor.value,
            js: window.jsEditor ? window.jsEditor.getValue() : templateJsEditor.value
        };
        
        // In a real application, you would send this data to the server
        // For now, we'll just show a success message
        alert('تم حفظ القالب بنجاح!');
        
        // Redirect to templates list
        window.location.href = 'templates.html';
    }
    
    /**
     * Validate the template form
     * @returns {boolean} - Whether the form is valid
     */
    function validateTemplateForm() {
        // Check if name is provided
        if (!templateNameInput.value) {
            alert('الرجاء إدخال اسم القالب');
            templateNameInput.focus();
            return false;
        }
        
        // Check if HTML content is provided
        const html = window.htmlEditor ? window.htmlEditor.getValue() : templateHtmlEditor.value;
        if (!html) {
            alert('الرجاء إدخال محتوى HTML للقالب');
            return false;
        }
        
        return true;
    }
});
