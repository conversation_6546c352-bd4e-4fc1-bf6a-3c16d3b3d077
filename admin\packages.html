<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الباقات | دعوات إلكترونية</title>
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/admin.css">
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <div class="admin-logo">
                    <div class="text-logo">دعوات إلكترونية</div>
                </div>
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="صورة المستخدم">
                </div>
                <div class="user-info">
                    <h3>أحمد محمد</h3>
                    <p>مدير النظام</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="index.html"><i class="fas fa-tachometer-alt"></i><span>لوحة التحكم</span></a></li>
                    <li><a href="users.html"><i class="fas fa-users"></i><span>المستخدمين</span></a></li>
                    <li><a href="invitations.html"><i class="fas fa-envelope-open-text"></i><span>الدعوات</span></a></li>
                    <li><a href="templates.html"><i class="fas fa-paint-brush"></i><span>قوالب التصميم</span></a></li>
                    <li class="active"><a href="packages.html"><i class="fas fa-box"></i><span>الباقات</span></a></li>
                    <li><a href="payments.html"><i class="fas fa-credit-card"></i><span>المدفوعات</span></a></li>
                    <li><a href="reports.html"><i class="fas fa-chart-bar"></i><span>التقارير</span></a></li>
                    <li><a href="settings.html"><i class="fas fa-cog"></i><span>الإعدادات</span></a></li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <a href="login.html" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="بحث...">
                </div>
                
                <div class="header-actions">
                    <div class="header-notification">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="header-message">
                        <i class="fas fa-envelope"></i>
                        <span class="message-badge">5</span>
                    </div>
                    <div class="header-profile">
                        <img src="images/admin-avatar.png" alt="صورة المستخدم">
                    </div>
                </div>
            </header>
            
            <div class="admin-content">
                <div class="page-header">
                    <h1>إدارة الباقات</h1>
                    <button class="btn btn-primary add-new-btn">
                        <i class="fas fa-plus"></i>
                        إضافة باقة جديدة
                    </button>
                </div>
                
                <!-- Packages List -->
                <div class="packages-container">
                    <!-- Package Card: Basic -->
                    <div class="package-card">
                        <div class="package-header">
                            <h3>الباقة الأساسية</h3>
                            <div class="package-price">
                                <span class="price">٧٩</span>
                                <span class="currency">ر.س</span>
                            </div>
                            <p class="price-period">شهريًا</p>
                        </div>
                        <div class="package-features">
                            <ul>
                                <li><i class="fas fa-check"></i> الارسال عبر الواتساب أو الايميل</li>
                                <li><i class="fas fa-check"></i> تأكيد الحضور</li>
                                <li><i class="fas fa-check"></i> كود لكل شخص</li>
                                <li><i class="fas fa-check"></i> موقع المناسبة</li>
                                <li><i class="fas fa-check"></i> استخدام تطبيق المسح الضوئي</li>
                            </ul>
                        </div>
                        <div class="package-stats">
                            <div class="stat">
                                <span class="stat-label">المشتركين</span>
                                <span class="stat-value">850</span>
                            </div>
                            <div class="stat">
                                <span class="stat-label">الإيرادات</span>
                                <span class="stat-value">67,150 ر.س</span>
                            </div>
                        </div>
                        <div class="package-actions">
                            <button class="btn btn-secondary edit-btn">تعديل</button>
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Package Card: Premium -->
                    <div class="package-card featured">
                        <div class="package-badge">الأكثر شعبية</div>
                        <div class="package-header">
                            <h3>الباقة المميزة</h3>
                            <div class="package-price">
                                <span class="price">٩٠٠</span>
                                <span class="currency">ر.س</span>
                            </div>
                            <p class="price-period">لكل مناسبة</p>
                        </div>
                        <div class="package-features">
                            <ul>
                                <li><i class="fas fa-check"></i> جميع مميزات الباقة الأساسية</li>
                                <li><i class="fas fa-check"></i> إدارة الدعوات</li>
                                <li><i class="fas fa-check"></i> موظفة تنظيم دخول يوم الحفل</li>
                                <li><i class="fas fa-check"></i> استبدال دعوات المعتذرين</li>
                                <li><i class="fas fa-check"></i> إمكانية توفير حساب فرعي</li>
                                <li><i class="fas fa-check"></i> عروض على التصاميم</li>
                            </ul>
                        </div>
                        <div class="package-stats">
                            <div class="stat">
                                <span class="stat-label">المشتركين</span>
                                <span class="stat-value">320</span>
                            </div>
                            <div class="stat">
                                <span class="stat-label">الإيرادات</span>
                                <span class="stat-value">288,000 ر.س</span>
                            </div>
                        </div>
                        <div class="package-actions">
                            <button class="btn btn-secondary edit-btn">تعديل</button>
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Package Card: Business -->
                    <div class="package-card">
                        <div class="package-header">
                            <h3>باقة البريميوم</h3>
                            <div class="package-price">
                                <span class="price">٥٠٠٠</span>
                                <span class="currency">ر.س</span>
                            </div>
                            <p class="price-period">سنويًا</p>
                        </div>
                        <div class="package-features">
                            <ul>
                                <li><i class="fas fa-check"></i> جميع مميزات الباقة الأساسية</li>
                                <li><i class="fas fa-check"></i> مدير حساب</li>
                                <li><i class="fas fa-check"></i> إمكانية توفير رقم خاص</li>
                                <li><i class="fas fa-check"></i> خيارات تخصيص الدعوة</li>
                                <li><i class="fas fa-check"></i> دعم فني على مدار الساعة</li>
                                <li><i class="fas fa-check"></i> تقارير وإحصائيات متقدمة</li>
                            </ul>
                        </div>
                        <div class="package-stats">
                            <div class="stat">
                                <span class="stat-label">المشتركين</span>
                                <span class="stat-value">80</span>
                            </div>
                            <div class="stat">
                                <span class="stat-label">الإيرادات</span>
                                <span class="stat-value">400,000 ر.س</span>
                            </div>
                        </div>
                        <div class="package-actions">
                            <button class="btn btn-secondary edit-btn">تعديل</button>
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="../js/main.js"></script>
    <style>
        /* Additional styles for packages page */
        .packages-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .package-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
            transition: transform 0.3s ease;
        }
        
        .package-card:hover {
            transform: translateY(-5px);
        }
        
        .package-card.featured {
            border: 2px solid var(--admin-primary);
            transform: scale(1.05);
        }
        
        .package-card.featured:hover {
            transform: scale(1.05) translateY(-5px);
        }
        
        .package-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background-color: var(--admin-primary);
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .package-header {
            padding: 30px;
            text-align: center;
            border-bottom: 1px solid var(--admin-border);
        }
        
        .package-header h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--admin-dark);
        }
        
        .package-price {
            display: flex;
            justify-content: center;
            align-items: baseline;
            margin-bottom: 5px;
        }
        
        .price {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--admin-primary);
        }
        
        .currency {
            font-size: 1.2rem;
            margin-right: 5px;
            color: var(--admin-gray);
        }
        
        .price-period {
            font-size: 0.9rem;
            color: var(--admin-gray);
        }
        
        .package-features {
            padding: 30px;
        }
        
        .package-features ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .package-features li {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .package-features li i {
            color: var(--admin-success);
            margin-left: 10px;
            font-size: 1rem;
        }
        
        .package-stats {
            display: flex;
            border-top: 1px solid var(--admin-border);
            border-bottom: 1px solid var(--admin-border);
        }
        
        .stat {
            flex: 1;
            padding: 15px;
            text-align: center;
        }
        
        .stat:first-child {
            border-left: 1px solid var(--admin-border);
        }
        
        .stat-label {
            display: block;
            font-size: 0.9rem;
            color: var(--admin-gray);
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--admin-dark);
        }
        
        .package-actions {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</body>
</html>
