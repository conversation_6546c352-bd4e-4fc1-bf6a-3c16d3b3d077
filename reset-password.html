<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور | دعوات إلكترونية</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Tajawal is a good Arabic font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/auth.css">
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="logo">
                    <a href="index.html">
                        <div class="text-logo">دعوات إلكترونية</div>
                    </a>
                </div>
                <div class="nav-links">
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="index.html#features">المميزات</a></li>
                        <li><a href="index.html#how-it-works">كيف يعمل</a></li>
                        <li><a href="index.html#testimonials">العملاء</a></li>
                        <li><a href="index.html#pricing">الباقات والأسعار</a></li>
                        <li><a href="index.html#contact">تواصل معنا</a></li>
                    </ul>
                </div>
                <div class="auth-buttons">
                    <a href="login.html" class="btn btn-login">تسجيل الدخول</a>
                    <a href="admin/login.html" class="btn btn-secondary">لوحة التحكم</a>
                </div>
                <div class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Reset Password Section -->
    <section class="auth-section">
        <div class="container">
            <div class="auth-container reset-password-container">
                <div class="auth-form-container">
                    <h2>إعادة تعيين كلمة المرور</h2>
                    <p>أدخل كلمة المرور الجديدة</p>
                    
                    <form class="auth-form" id="reset-password-form">
                        <div class="form-group">
                            <label for="password">كلمة المرور الجديدة</label>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="password" name="password" placeholder="أدخل كلمة المرور الجديدة" required>
                            </div>
                            <div class="password-strength">
                                <div class="strength-meter">
                                    <div class="strength-meter-fill" data-strength="0"></div>
                                </div>
                                <div class="strength-text">قوة كلمة المرور: <span>ضعيفة</span></div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm-password">تأكيد كلمة المرور</label>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="confirm-password" name="confirm-password" placeholder="أعد إدخال كلمة المرور الجديدة" required>
                            </div>
                        </div>
                        
                        <div class="password-requirements">
                            <h4>متطلبات كلمة المرور:</h4>
                            <ul>
                                <li id="length-check"><i class="fas fa-times-circle"></i> 8 أحرف على الأقل</li>
                                <li id="uppercase-check"><i class="fas fa-times-circle"></i> حرف كبير واحد على الأقل</li>
                                <li id="lowercase-check"><i class="fas fa-times-circle"></i> حرف صغير واحد على الأقل</li>
                                <li id="number-check"><i class="fas fa-times-circle"></i> رقم واحد على الأقل</li>
                                <li id="special-check"><i class="fas fa-times-circle"></i> رمز خاص واحد على الأقل (!@#$%^&*)</li>
                            </ul>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block" id="submit-btn" disabled>تعيين كلمة المرور</button>
                    </form>
                    
                    <div class="auth-footer">
                        <p>تذكرت كلمة المرور القديمة؟ <a href="login.html">تسجيل الدخول</a></p>
                    </div>
                </div>
                
                <div class="auth-image">
                    <div class="auth-image-content">
                        <h3>إعادة تعيين كلمة المرور</h3>
                        <p>قم بإنشاء كلمة مرور قوية وآمنة لحماية حسابك. تأكد من استخدام مزيج من الأحرف الكبيرة والصغيرة والأرقام والرموز الخاصة.</p>
                        <div class="auth-tips">
                            <h4>نصائح لكلمة مرور آمنة:</h4>
                            <ul>
                                <li>استخدم كلمة مرور فريدة لكل حساب</li>
                                <li>تجنب استخدام معلومات شخصية يسهل تخمينها</li>
                                <li>لا تشارك كلمة المرور مع أي شخص</li>
                                <li>قم بتغيير كلمة المرور بشكل دوري</li>
                                <li>فكر في استخدام جملة مرور بدلاً من كلمة مرور</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Success Message (Hidden by default) -->
            <div class="auth-success-message" id="success-message" style="display: none;">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2>تم إعادة تعيين كلمة المرور بنجاح</h2>
                <p>تم تغيير كلمة المرور الخاصة بك بنجاح. يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.</p>
                <div class="success-actions">
                    <a href="login.html" class="btn btn-primary">تسجيل الدخول</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <div class="text-logo">دعوات إلكترونية</div>
                    <p>شركة للاتصالات وتقنية المعلومات</p>
                    <p>الرياض - المملكة العربية السعودية</p>
                </div>
                <div class="footer-links">
                    <div class="footer-links-column">
                        <h3>روابط سريعة</h3>
                        <ul>
                            <li><a href="index.html">الرئيسية</a></li>
                            <li><a href="index.html#features">المميزات</a></li>
                            <li><a href="index.html#how-it-works">كيف يعمل</a></li>
                            <li><a href="index.html#testimonials">العملاء</a></li>
                            <li><a href="index.html#pricing">الباقات والأسعار</a></li>
                            <li><a href="index.html#contact">تواصل معنا</a></li>
                        </ul>
                    </div>
                    <div class="footer-links-column">
                        <h3>الشروط والأحكام</h3>
                        <ul>
                            <li><a href="terms.html">شروط الاستخدام</a></li>
                            <li><a href="privacy.html">سياسة الخصوصية</a></li>
                            <li><a href="faq.html">الأسئلة الشائعة</a></li>
                        </ul>
                    </div>
                </div>
                <div class="footer-social">
                    <h3>تواصل معنا</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-whatsapp"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© 2025 جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resetPasswordForm = document.getElementById('reset-password-form');
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirm-password');
            const submitBtn = document.getElementById('submit-btn');
            const authContainer = document.querySelector('.auth-container');
            const successMessage = document.getElementById('success-message');
            
            // Password strength elements
            const strengthMeter = document.querySelector('.strength-meter-fill');
            const strengthText = document.querySelector('.strength-text span');
            
            // Password requirement checks
            const lengthCheck = document.getElementById('length-check');
            const uppercaseCheck = document.getElementById('uppercase-check');
            const lowercaseCheck = document.getElementById('lowercase-check');
            const numberCheck = document.getElementById('number-check');
            const specialCheck = document.getElementById('special-check');
            
            // Check password strength
            function checkPasswordStrength(password) {
                let strength = 0;
                
                // Check length
                if (password.length >= 8) {
                    strength += 1;
                    lengthCheck.innerHTML = '<i class="fas fa-check-circle"></i> 8 أحرف على الأقل';
                } else {
                    lengthCheck.innerHTML = '<i class="fas fa-times-circle"></i> 8 أحرف على الأقل';
                }
                
                // Check uppercase
                if (/[A-Z]/.test(password)) {
                    strength += 1;
                    uppercaseCheck.innerHTML = '<i class="fas fa-check-circle"></i> حرف كبير واحد على الأقل';
                } else {
                    uppercaseCheck.innerHTML = '<i class="fas fa-times-circle"></i> حرف كبير واحد على الأقل';
                }
                
                // Check lowercase
                if (/[a-z]/.test(password)) {
                    strength += 1;
                    lowercaseCheck.innerHTML = '<i class="fas fa-check-circle"></i> حرف صغير واحد على الأقل';
                } else {
                    lowercaseCheck.innerHTML = '<i class="fas fa-times-circle"></i> حرف صغير واحد على الأقل';
                }
                
                // Check numbers
                if (/[0-9]/.test(password)) {
                    strength += 1;
                    numberCheck.innerHTML = '<i class="fas fa-check-circle"></i> رقم واحد على الأقل';
                } else {
                    numberCheck.innerHTML = '<i class="fas fa-times-circle"></i> رقم واحد على الأقل';
                }
                
                // Check special characters
                if (/[!@#$%^&*]/.test(password)) {
                    strength += 1;
                    specialCheck.innerHTML = '<i class="fas fa-check-circle"></i> رمز خاص واحد على الأقل (!@#$%^&*)';
                } else {
                    specialCheck.innerHTML = '<i class="fas fa-times-circle"></i> رمز خاص واحد على الأقل (!@#$%^&*)';
                }
                
                return strength;
            }
            
            // Update password strength UI
            function updateStrengthUI(strength) {
                // Update strength meter
                strengthMeter.style.width = (strength * 20) + '%';
                strengthMeter.setAttribute('data-strength', strength);
                
                // Update strength text
                if (strength === 0) {
                    strengthText.textContent = 'ضعيفة جداً';
                } else if (strength === 1) {
                    strengthText.textContent = 'ضعيفة';
                } else if (strength === 2) {
                    strengthText.textContent = 'متوسطة';
                } else if (strength === 3) {
                    strengthText.textContent = 'جيدة';
                } else if (strength === 4) {
                    strengthText.textContent = 'قوية';
                } else if (strength === 5) {
                    strengthText.textContent = 'قوية جداً';
                }
                
                // Enable/disable submit button
                if (strength >= 3 && password.value === confirmPassword.value && password.value !== '') {
                    submitBtn.disabled = false;
                } else {
                    submitBtn.disabled = true;
                }
            }
            
            // Password input event
            if (password) {
                password.addEventListener('input', function() {
                    const strength = checkPasswordStrength(this.value);
                    updateStrengthUI(strength);
                });
            }
            
            // Confirm password input event
            if (confirmPassword) {
                confirmPassword.addEventListener('input', function() {
                    const strength = checkPasswordStrength(password.value);
                    updateStrengthUI(strength);
                });
            }
            
            // Form submission
            if (resetPasswordForm) {
                resetPasswordForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // Check if passwords match
                    if (password.value !== confirmPassword.value) {
                        alert('كلمات المرور غير متطابقة');
                        return;
                    }
                    
                    // Simulate API call to reset password
                    // In a real application, this would be an AJAX call to your backend
                    
                    // Show success message
                    authContainer.style.display = 'none';
                    successMessage.style.display = 'block';
                });
            }
        });
    </script>
    <style>
        /* Additional styles for reset password page */
        .reset-password-container {
            max-width: 900px;
        }
        
        .password-strength {
            margin-top: 10px;
        }
        
        .strength-meter {
            height: 5px;
            background-color: #e9ecef;
            border-radius: 5px;
            margin-bottom: 5px;
        }
        
        .strength-meter-fill {
            height: 100%;
            border-radius: 5px;
            transition: width 0.3s ease;
        }
        
        .strength-meter-fill[data-strength="0"] {
            width: 0%;
            background-color: transparent;
        }
        
        .strength-meter-fill[data-strength="1"] {
            width: 20%;
            background-color: #ff3b30;
        }
        
        .strength-meter-fill[data-strength="2"] {
            width: 40%;
            background-color: #ff9500;
        }
        
        .strength-meter-fill[data-strength="3"] {
            width: 60%;
            background-color: #ffcc00;
        }
        
        .strength-meter-fill[data-strength="4"] {
            width: 80%;
            background-color: #34c759;
        }
        
        .strength-meter-fill[data-strength="5"] {
            width: 100%;
            background-color: #4cd964;
        }
        
        .strength-text {
            font-size: 0.8rem;
            color: #6c757d;
            text-align: left;
        }
        
        .password-requirements {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .password-requirements h4 {
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .password-requirements ul {
            list-style: none;
            padding: 0;
        }
        
        .password-requirements li {
            font-size: 0.85rem;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        
        .password-requirements li i {
            margin-left: 8px;
            font-size: 0.9rem;
        }
        
        .password-requirements .fa-times-circle {
            color: #ff3b30;
        }
        
        .password-requirements .fa-check-circle {
            color: #4cd964;
        }
        
        .auth-tips {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .auth-tips h4 {
            font-size: 1.1rem;
            margin-bottom: 15px;
        }
        
        .auth-tips ul {
            padding-right: 20px;
        }
        
        .auth-tips li {
            margin-bottom: 10px;
            position: relative;
        }
        
        .auth-tips li::before {
            content: "•";
            position: absolute;
            right: -20px;
            color: rgba(255, 255, 255, 0.7);
        }
    </style>
</body>
</html>
