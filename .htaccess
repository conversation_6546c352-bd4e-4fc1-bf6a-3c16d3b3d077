# تفعيل محرك إعادة الكتابة
RewriteEngine On

# تعيين المجلد الأساسي
# RewriteBase /

# منع الوصول إلى ملفات معينة
<FilesMatch "^(config\.php|database\.sql|db_connection\.php)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع عرض محتويات المجلدات
Options -Indexes

# إعادة توجيه HTTP إلى HTTPS (قم بإلغاء التعليق عند استخدام HTTPS)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# تعيين ترميز الملفات
AddDefaultCharset UTF-8

# تعيين منطقة زمنية
SetEnv TZ Asia/Riyadh

# تفعيل ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# تعيين التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# تعيين رأس الأمان
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" env=HTTPS
</IfModule>

# منع الوصول إلى ملفات .htaccess و .htpasswd
<Files ~ "^\.ht">
    Order Allow,Deny
    Deny from all
</Files>

# منع الوصول إلى ملفات النسخ الاحتياطي والمؤقتة
<FilesMatch "(\.(bak|config|sql|fla|psd|ini|log|sh|inc|swp|dist)|~)$">
    Order Allow,Deny
    Deny from all
    Satisfy All
</FilesMatch>

# تعيين صفحة الخطأ 404
ErrorDocument 404 /404.html

# تعيين صفحة الخطأ 500
ErrorDocument 500 /500.html
