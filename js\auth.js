/**
 * Authentication Module
 * This module handles user authentication functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const loginForm = document.querySelector('.auth-form');
    const adminLoginForm = document.querySelector('.admin-login-form');
    const registerForm = document.querySelector('.register-form');
    
    // Initialize authentication
    initAuth();
    
    /**
     * Initialize authentication functionality
     */
    function initAuth() {
        // Add event listeners for login form
        if (loginForm) {
            loginForm.addEventListener('submit', handleLogin);
        }
        
        // Add event listeners for admin login form
        if (adminLoginForm) {
            adminLoginForm.addEventListener('submit', handleAdminLogin);
        }
        
        // Add event listeners for register form
        if (registerForm) {
            registerForm.addEventListener('submit', handleRegister);
        }
        
        // Check if user is already logged in
        checkAuthStatus();
    }
    
    /**
     * Handle login form submission
     * @param {Event} e - The form submission event
     */
    function handleLogin(e) {
        e.preventDefault();
        
        // Get form data
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('remember').checked;
        
        // Validate form data
        if (!validateLoginForm(email, password)) {
            return;
        }
        
        // In a real application, you would send this data to the server
        // For now, we'll just simulate a successful login
        
        // Check credentials (this is just a simple example)
        if (email === '<EMAIL>' && password === 'password123') {
            // Save user data to localStorage
            const userData = {
                id: '1',
                email: email,
                name: 'مستخدم تجريبي',
                role: 'user'
            };
            
            saveUserData(userData, rememberMe);
            
            // Show success message
            showMessage('تم تسجيل الدخول بنجاح', 'success');
            
            // Redirect to dashboard
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        } else {
            // Show error message
            showMessage('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'error');
        }
    }
    
    /**
     * Handle admin login form submission
     * @param {Event} e - The form submission event
     */
    function handleAdminLogin(e) {
        e.preventDefault();
        
        // Get form data
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('remember').checked;
        
        // Validate form data
        if (!validateLoginForm(email, password)) {
            return;
        }
        
        // In a real application, you would send this data to the server
        // For now, we'll just simulate a successful login
        
        // Check credentials (this is just a simple example)
        if (email === '<EMAIL>' && password === 'admin123') {
            // Save user data to localStorage
            const userData = {
                id: 'admin1',
                email: email,
                name: 'أحمد محمد',
                role: 'admin'
            };
            
            saveUserData(userData, rememberMe);
            
            // Show success message
            showMessage('تم تسجيل الدخول بنجاح', 'success');
            
            // Redirect to admin dashboard
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        } else {
            // Show error message
            showMessage('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'error');
        }
    }
    
    /**
     * Handle register form submission
     * @param {Event} e - The form submission event
     */
    function handleRegister(e) {
        e.preventDefault();
        
        // Get form data
        const name = document.getElementById('name').value;
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm-password').value;
        const agreeTerms = document.getElementById('agree-terms').checked;
        
        // Validate form data
        if (!validateRegisterForm(name, email, password, confirmPassword, agreeTerms)) {
            return;
        }
        
        // In a real application, you would send this data to the server
        // For now, we'll just simulate a successful registration
        
        // Save user data to localStorage
        const userData = {
            id: Date.now().toString(),
            email: email,
            name: name,
            role: 'user'
        };
        
        saveUserData(userData, true);
        
        // Show success message
        showMessage('تم إنشاء الحساب بنجاح', 'success');
        
        // Redirect to dashboard
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1500);
    }
    
    /**
     * Validate login form data
     * @param {string} email - The email address
     * @param {string} password - The password
     * @returns {boolean} - Whether the form data is valid
     */
    function validateLoginForm(email, password) {
        // Check if email is valid
        if (!isValidEmail(email)) {
            showMessage('يرجى إدخال بريد إلكتروني صحيح', 'error');
            return false;
        }
        
        // Check if password is not empty
        if (!password) {
            showMessage('يرجى إدخال كلمة المرور', 'error');
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate register form data
     * @param {string} name - The user's name
     * @param {string} email - The email address
     * @param {string} password - The password
     * @param {string} confirmPassword - The password confirmation
     * @param {boolean} agreeTerms - Whether the user agreed to the terms
     * @returns {boolean} - Whether the form data is valid
     */
    function validateRegisterForm(name, email, password, confirmPassword, agreeTerms) {
        // Check if name is not empty
        if (!name) {
            showMessage('يرجى إدخال الاسم', 'error');
            return false;
        }
        
        // Check if email is valid
        if (!isValidEmail(email)) {
            showMessage('يرجى إدخال بريد إلكتروني صحيح', 'error');
            return false;
        }
        
        // Check if password is at least 8 characters
        if (password.length < 8) {
            showMessage('يجب أن تكون كلمة المرور 8 أحرف على الأقل', 'error');
            return false;
        }
        
        // Check if passwords match
        if (password !== confirmPassword) {
            showMessage('كلمات المرور غير متطابقة', 'error');
            return false;
        }
        
        // Check if user agreed to terms
        if (!agreeTerms) {
            showMessage('يجب الموافقة على الشروط والأحكام', 'error');
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if email is valid
     * @param {string} email - The email address
     * @returns {boolean} - Whether the email is valid
     */
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    /**
     * Save user data to localStorage
     * @param {Object} userData - The user data
     * @param {boolean} rememberMe - Whether to remember the user
     */
    function saveUserData(userData, rememberMe) {
        // Save user data to localStorage
        localStorage.setItem('userData', JSON.stringify(userData));
        
        // Save auth token (in a real application, this would be a JWT)
        const token = generateToken();
        
        if (rememberMe) {
            // Save to localStorage (persists even after browser is closed)
            localStorage.setItem('authToken', token);
        } else {
            // Save to sessionStorage (cleared when browser is closed)
            sessionStorage.setItem('authToken', token);
        }
    }
    
    /**
     * Generate a random token
     * @returns {string} - A random token
     */
    function generateToken() {
        // In a real application, this would be a JWT from the server
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }
    
    /**
     * Check if user is already logged in
     */
    function checkAuthStatus() {
        // Check if user is logged in
        const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
        const userData = JSON.parse(localStorage.getItem('userData'));
        
        if (token && userData) {
            // User is logged in
            updateUIForLoggedInUser(userData);
        }
    }
    
    /**
     * Update UI for logged in user
     * @param {Object} userData - The user data
     */
    function updateUIForLoggedInUser(userData) {
        // Update navigation bar
        const authButtons = document.querySelector('.auth-buttons');
        
        if (authButtons) {
            authButtons.innerHTML = `
                <div class="user-dropdown">
                    <button class="user-dropdown-btn">
                        <span>${userData.name}</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-dropdown-content">
                        <a href="profile.html">
                            <i class="fas fa-user"></i>
                            الملف الشخصي
                        </a>
                        <a href="my-invitations.html">
                            <i class="fas fa-envelope-open-text"></i>
                            دعواتي
                        </a>
                        ${userData.role === 'admin' ? `
                            <a href="admin/index.html">
                                <i class="fas fa-tachometer-alt"></i>
                                لوحة التحكم
                            </a>
                        ` : ''}
                        <a href="#" id="logout-btn">
                            <i class="fas fa-sign-out-alt"></i>
                            تسجيل الخروج
                        </a>
                    </div>
                </div>
            `;
            
            // Add event listener for logout button
            const logoutBtn = document.getElementById('logout-btn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', handleLogout);
            }
            
            // Add event listener for user dropdown
            const userDropdownBtn = document.querySelector('.user-dropdown-btn');
            if (userDropdownBtn) {
                userDropdownBtn.addEventListener('click', function() {
                    const dropdownContent = document.querySelector('.user-dropdown-content');
                    dropdownContent.classList.toggle('show');
                });
                
                // Close the dropdown when clicking outside
                window.addEventListener('click', function(e) {
                    if (!e.target.matches('.user-dropdown-btn') && !e.target.matches('.user-dropdown-btn *')) {
                        const dropdownContent = document.querySelector('.user-dropdown-content');
                        if (dropdownContent && dropdownContent.classList.contains('show')) {
                            dropdownContent.classList.remove('show');
                        }
                    }
                });
            }
        }
    }
    
    /**
     * Handle logout
     * @param {Event} e - The click event
     */
    function handleLogout(e) {
        e.preventDefault();
        
        // Clear auth data
        localStorage.removeItem('authToken');
        sessionStorage.removeItem('authToken');
        localStorage.removeItem('userData');
        
        // Show success message
        showMessage('تم تسجيل الخروج بنجاح', 'success');
        
        // Redirect to home page
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1500);
    }
    
    /**
     * Show a message to the user
     * @param {string} message - The message to show
     * @param {string} type - The message type (success, error, info)
     */
    function showMessage(message, type) {
        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = `message message-${type}`;
        messageElement.innerHTML = `
            <div class="message-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-times-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;
        
        // Add message to the page
        document.body.appendChild(messageElement);
        
        // Show message
        setTimeout(() => {
            messageElement.classList.add('show');
        }, 10);
        
        // Hide message after 3 seconds
        setTimeout(() => {
            messageElement.classList.remove('show');
            
            // Remove message from the page after animation
            setTimeout(() => {
                messageElement.remove();
            }, 300);
        }, 3000);
    }
});

// Add CSS for messages
document.addEventListener('DOMContentLoaded', function() {
    // Create style element
    const style = document.createElement('style');
    
    // Add message styles
    style.textContent = `
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 300px;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            transform: translateY(-20px);
            opacity: 0;
            transition: all 0.3s;
        }
        
        .message.show {
            transform: translateY(0);
            opacity: 1;
        }
        
        .message-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .message-success {
            background-color: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .message-error {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        .message-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }
        
        .user-dropdown {
            position: relative;
            display: inline-block;
        }
        
        .user-dropdown-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Tajawal', sans-serif;
            font-size: 0.9rem;
            transition: all 0.3s;
        }
        
        .user-dropdown-btn:hover {
            background-color: var(--primary-dark);
        }
        
        .user-dropdown-content {
            position: absolute;
            top: 100%;
            left: 0;
            width: 200px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            display: none;
            overflow: hidden;
        }
        
        .user-dropdown-content.show {
            display: block;
        }
        
        .user-dropdown-content a {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .user-dropdown-content a:hover {
            background-color: var(--light-bg);
        }
        
        .user-dropdown-content a i {
            width: 20px;
            text-align: center;
            color: var(--primary-color);
        }
    `;
    
    // Add style to the page
    document.head.appendChild(style);
});
