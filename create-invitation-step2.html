<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء دعوة جديدة - تفاصيل المناسبة | دعوات إلكترونية</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="logo">
                    <a href="index.html">
                        <div class="text-logo">دعوات إلكترونية</div>
                    </a>
                </div>
                <div class="nav-links">
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="index.html#features">المميزات</a></li>
                        <li><a href="index.html#how-it-works">كيف يعمل</a></li>
                        <li><a href="index.html#testimonials">العملاء</a></li>
                        <li><a href="index.html#pricing">الباقات والأسعار</a></li>
                        <li><a href="index.html#contact">تواصل معنا</a></li>
                    </ul>
                </div>
                <div class="auth-buttons">
                    <a href="login.html" class="btn btn-login">تسجيل الدخول</a>
                    <a href="admin/login.html" class="btn btn-secondary">لوحة التحكم</a>
                </div>
                <div class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Create Invitation Section -->
    <section class="create-invitation-section">
        <div class="container">
            <div class="page-header">
                <h1>إنشاء دعوة جديدة</h1>
                <p>قم بإنشاء دعوة مخصصة لمناسبتك الخاصة</p>
            </div>
            
            <!-- Progress Steps -->
            <div class="progress-steps">
                <div class="step completed" data-step="1">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-label">اختيار القالب</div>
                </div>
                <div class="step active" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-label">تفاصيل المناسبة</div>
                </div>
                <div class="step" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-label">تخصيص التصميم</div>
                </div>
                <div class="step" data-step="4">
                    <div class="step-number">4</div>
                    <div class="step-label">المدعوين</div>
                </div>
                <div class="step" data-step="5">
                    <div class="step-number">5</div>
                    <div class="step-label">المعاينة والنشر</div>
                </div>
            </div>
            
            <!-- Form Container -->
            <div class="create-invitation-container">
                <!-- Step 2: Event Details -->
                <div class="step-content active" id="step-2">
                    <h2>تفاصيل المناسبة</h2>
                    
                    <form class="event-details-form">
                        <div class="form-section">
                            <h3>معلومات أساسية</h3>
                            
                            <div class="form-group">
                                <label for="event-title">عنوان المناسبة <span class="required">*</span></label>
                                <input type="text" id="event-title" name="event-title" placeholder="مثال: حفل زفاف نورة وأحمد" required>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="event-type">نوع المناسبة <span class="required">*</span></label>
                                    <select id="event-type" name="event-type" required>
                                        <option value="" disabled>اختر نوع المناسبة</option>
                                        <option value="wedding" selected>زفاف</option>
                                        <option value="birthday">عيد ميلاد</option>
                                        <option value="graduation">تخرج</option>
                                        <option value="conference">مؤتمر</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="event-language">لغة الدعوة <span class="required">*</span></label>
                                    <select id="event-language" name="event-language" required>
                                        <option value="ar" selected>العربية</option>
                                        <option value="en">الإنجليزية</option>
                                        <option value="both">العربية والإنجليزية</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="event-description">وصف المناسبة</label>
                                <textarea id="event-description" name="event-description" rows="3" placeholder="أضف وصفًا مختصرًا للمناسبة (اختياري)"></textarea>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <h3>التاريخ والوقت</h3>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="event-date">تاريخ المناسبة <span class="required">*</span></label>
                                    <input type="date" id="event-date" name="event-date" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="event-time">وقت المناسبة <span class="required">*</span></label>
                                    <input type="time" id="event-time" name="event-time" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="event-duration">مدة المناسبة</label>
                                <select id="event-duration" name="event-duration">
                                    <option value="" disabled>اختر المدة</option>
                                    <option value="1">ساعة واحدة</option>
                                    <option value="2" selected>ساعتان</option>
                                    <option value="3">3 ساعات</option>
                                    <option value="4">4 ساعات</option>
                                    <option value="5">5 ساعات</option>
                                    <option value="all-day">طوال اليوم</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <h3>الموقع</h3>
                            
                            <div class="form-group">
                                <label for="event-venue">اسم المكان <span class="required">*</span></label>
                                <input type="text" id="event-venue" name="event-venue" placeholder="مثال: قاعة الأميرة" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="event-address">العنوان <span class="required">*</span></label>
                                <input type="text" id="event-address" name="event-address" placeholder="مثال: شارع الملك فهد، حي العليا، الرياض" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="event-location">الموقع على الخريطة</label>
                                <div class="map-container">
                                    <div id="map" class="map-placeholder">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <p>انقر لتحديد الموقع على الخريطة</p>
                                    </div>
                                </div>
                                <input type="hidden" id="event-location" name="event-location">
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <h3>معلومات إضافية</h3>
                            
                            <div class="form-group">
                                <label for="dress-code">قواعد اللباس</label>
                                <select id="dress-code" name="dress-code">
                                    <option value="" selected>بدون تحديد</option>
                                    <option value="formal">رسمي</option>
                                    <option value="semi-formal">شبه رسمي</option>
                                    <option value="casual">غير رسمي</option>
                                    <option value="traditional">تقليدي</option>
                                    <option value="black-tie">بدلة سوداء</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="additional-info">ملاحظات إضافية</label>
                                <textarea id="additional-info" name="additional-info" rows="3" placeholder="أي معلومات إضافية ترغب في إضافتها للمدعوين"></textarea>
                            </div>
                            
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="rsvp-required" name="rsvp-required" checked>
                                    <label for="rsvp-required">تفعيل خاصية تأكيد الحضور (RSVP)</label>
                                </div>
                            </div>
                            
                            <div class="form-group rsvp-options">
                                <label for="rsvp-deadline">الموعد النهائي لتأكيد الحضور</label>
                                <input type="date" id="rsvp-deadline" name="rsvp-deadline">
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- Navigation Buttons -->
                <div class="form-navigation">
                    <button class="btn btn-secondary" id="prev-btn">السابق</button>
                    <button class="btn btn-primary" id="next-btn">التالي</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <div class="text-logo">دعوات إلكترونية</div>
                    <p>شركة للاتصالات وتقنية المعلومات</p>
                    <p>الرياض - المملكة العربية السعودية</p>
                </div>
                <div class="footer-links">
                    <div class="footer-links-column">
                        <h3>روابط سريعة</h3>
                        <ul>
                            <li><a href="index.html">الرئيسية</a></li>
                            <li><a href="index.html#features">المميزات</a></li>
                            <li><a href="index.html#how-it-works">كيف يعمل</a></li>
                            <li><a href="index.html#testimonials">العملاء</a></li>
                            <li><a href="index.html#pricing">الباقات والأسعار</a></li>
                            <li><a href="index.html#contact">تواصل معنا</a></li>
                        </ul>
                    </div>
                    <div class="footer-links-column">
                        <h3>الشروط والأحكام</h3>
                        <ul>
                            <li><a href="terms.html">شروط الاستخدام</a></li>
                            <li><a href="privacy.html">سياسة الخصوصية</a></li>
                            <li><a href="faq.html">الأسئلة الشائعة</a></li>
                        </ul>
                    </div>
                </div>
                <div class="footer-social">
                    <h3>تواصل معنا</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-whatsapp"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© 2025 جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <style>
        /* Create Invitation Styles */
        .create-invitation-section {
            padding: 60px 0;
            background-color: #f8f9fa;
            min-height: 80vh;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #333;
        }
        
        .page-header p {
            font-size: 1.1rem;
            color: #666;
        }
        
        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
            position: relative;
        }
        
        .progress-steps::before {
            content: "";
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #e0e0e0;
            z-index: 1;
        }
        
        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e0e0e0;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-bottom: 10px;
            transition: all 0.3s;
        }
        
        .step.active .step-number {
            background-color: var(--primary-color);
            color: white;
        }
        
        .step.completed .step-number {
            background-color: #4cd964;
            color: white;
        }
        
        .step-label {
            font-size: 0.9rem;
            color: #666;
            transition: all 0.3s;
        }
        
        .step.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .step.completed .step-label {
            color: #4cd964;
            font-weight: 600;
        }
        
        .create-invitation-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-bottom: 40px;
        }
        
        .step-content {
            display: none;
        }
        
        .step-content.active {
            display: block;
        }
        
        .step-content h2 {
            font-size: 1.5rem;
            margin-bottom: 25px;
            color: #333;
            text-align: center;
        }
        
        .form-section {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .form-section h3 {
            font-size: 1.2rem;
            margin-bottom: 20px;
            color: #333;
            position: relative;
            padding-right: 15px;
        }
        
        .form-section h3::before {
            content: "";
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 20px;
            background-color: var(--primary-color);
            border-radius: 3px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            font-size: 0.95rem;
            font-weight: 500;
            color: #555;
            margin-bottom: 8px;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .required {
            color: #ff3b30;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .map-container {
            width: 100%;
            height: 200px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .map-placeholder {
            width: 100%;
            height: 100%;
            background-color: #f0f0f0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #666;
            cursor: pointer;
        }
        
        .map-placeholder i {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .form-navigation {
            display: flex;
            justify-content: space-between;
        }
        
        /* Responsive styles */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .progress-steps {
                overflow-x: auto;
                padding-bottom: 15px;
            }
            
            .step {
                min-width: 100px;
            }
        }
    </style>
</body>
</html>
