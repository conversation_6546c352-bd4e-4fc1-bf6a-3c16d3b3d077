/* Admin Panel Styles */

/* Base Styles */
:root {
    --admin-primary: #6c63ff;
    --admin-secondary: #4a42e8;
    --admin-success: #4cd964;
    --admin-warning: #ff9500;
    --admin-danger: #ff3b30;
    --admin-info: #5ac8fa;
    --admin-light: #f8f9fa;
    --admin-dark: #343a40;
    --admin-gray: #6c757d;
    --admin-gray-light: #e9ecef;
    --admin-gray-dark: #495057;
    --admin-border: #dee2e6;
    --admin-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --admin-transition: all 0.3s ease;
    --admin-sidebar-width: 260px;
    --admin-sidebar-collapsed-width: 70px;
    --admin-header-height: 70px;
}

.admin-body {
    background-color: #f5f5f9;
    font-family: 'Ta<PERSON><PERSON>', sans-serif;
}

/* Admin Login Styles */
.admin-login-container {
    display: flex;
    min-height: 100vh;
}

.admin-login-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 2rem;
    max-width: 500px;
    margin: 0 auto;
}

.admin-login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.admin-logo {
    margin-bottom: 1.5rem;
}

.admin-login-header h1 {
    font-size: 1.8rem;
    color: var(--admin-dark);
    margin-bottom: 0.5rem;
}

.admin-login-header p {
    color: var(--admin-gray);
}

.admin-login-form {
    margin-bottom: 1.5rem;
}

.admin-login-footer {
    text-align: center;
    margin-top: 1rem;
    color: var(--admin-gray);
}

.admin-login-footer a {
    color: var(--admin-primary);
    text-decoration: none;
}

.admin-login-footer a:hover {
    text-decoration: underline;
}

.admin-login-info {
    flex: 1;
    background-color: var(--admin-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.admin-login-info::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0) 100%);
}

.admin-info-content {
    position: relative;
    z-index: 1;
    max-width: 500px;
}

.admin-info-content h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
}

.admin-info-content p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.admin-info-content ul {
    margin-bottom: 2rem;
    padding-right: 1.5rem;
}

.admin-info-content li {
    margin-bottom: 0.75rem;
    position: relative;
}

.admin-info-content li::before {
    content: "•";
    position: absolute;
    right: -1.5rem;
    color: var(--admin-success);
}

.admin-info-note {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.admin-info-note i {
    font-size: 1.5rem;
    color: var(--admin-warning);
}

.admin-info-note a {
    color: white;
    text-decoration: underline;
}

/* Admin Layout Styles */
.admin-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

.admin-sidebar {
    width: var(--admin-sidebar-width);
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    transition: var(--admin-transition);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid var(--admin-border);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--admin-gray);
    font-size: 1.25rem;
    cursor: pointer;
    transition: var(--admin-transition);
}

.sidebar-toggle:hover {
    color: var(--admin-primary);
}

.sidebar-user {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--admin-border);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-left: 1rem;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info h3 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.user-info p {
    font-size: 0.875rem;
    color: var(--admin-gray);
}

.sidebar-nav {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 0.5rem;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--admin-gray-dark);
    text-decoration: none;
    transition: var(--admin-transition);
    border-right: 3px solid transparent;
}

.sidebar-nav a:hover {
    background-color: var(--admin-gray-light);
    color: var(--admin-primary);
}

.sidebar-nav a.active,
.sidebar-nav li.active a {
    background-color: rgba(108, 99, 255, 0.1);
    color: var(--admin-primary);
    border-right-color: var(--admin-primary);
}

.sidebar-nav i {
    font-size: 1.25rem;
    margin-left: 1rem;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid var(--admin-border);
}

.logout-btn {
    display: flex;
    align-items: center;
    color: var(--admin-danger);
    text-decoration: none;
    transition: var(--admin-transition);
}

.logout-btn:hover {
    color: #ff1a1a;
}

.logout-btn i {
    margin-left: 0.5rem;
}

.admin-main {
    flex: 1;
    margin-right: var(--admin-sidebar-width);
    transition: var(--admin-transition);
}

.admin-header {
    height: var(--admin-header-height);
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    position: sticky;
    top: 0;
    z-index: 900;
}

.header-search {
    position: relative;
    width: 300px;
}

.header-search i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--admin-gray);
}

.header-search input {
    width: 100%;
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    border: 1px solid var(--admin-border);
    border-radius: 50px;
    font-family: 'Tajawal', sans-serif;
}

.header-actions {
    display: flex;
    align-items: center;
}

.header-notification,
.header-message {
    position: relative;
    margin-left: 1.5rem;
    font-size: 1.25rem;
    color: var(--admin-gray);
    cursor: pointer;
    transition: var(--admin-transition);
}

.header-notification:hover,
.header-message:hover {
    color: var(--admin-primary);
}

.notification-badge,
.message-badge {
    position: absolute;
    top: -5px;
    left: -5px;
    background-color: var(--admin-danger);
    color: white;
    font-size: 0.75rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-profile {
    margin-right: 1.5rem;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
}

.header-profile img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.admin-content {
    padding: 1.5rem;
}

/* Sidebar Collapsed State */
.admin-container.sidebar-collapsed .admin-sidebar {
    width: var(--admin-sidebar-collapsed-width);
}

.admin-container.sidebar-collapsed .admin-main {
    margin-right: var(--admin-sidebar-collapsed-width);
}

.admin-container.sidebar-collapsed .sidebar-user {
    justify-content: center;
    padding: 1rem 0;
}

.admin-container.sidebar-collapsed .user-info,
.admin-container.sidebar-collapsed .sidebar-nav span,
.admin-container.sidebar-collapsed .logout-btn span {
    display: none;
}

.admin-container.sidebar-collapsed .user-avatar {
    margin-left: 0;
}

.admin-container.sidebar-collapsed .sidebar-nav a {
    justify-content: center;
    padding: 0.75rem 0;
}

.admin-container.sidebar-collapsed .sidebar-nav i {
    margin-left: 0;
}

.admin-container.sidebar-collapsed .logout-btn {
    justify-content: center;
}

/* Dashboard Styles */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.page-header h1 {
    font-size: 1.75rem;
    color: var(--admin-dark);
}

.page-header p {
    color: var(--admin-gray);
    margin-top: 0.25rem;
}

.add-new-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-left: 1rem;
}

.stat-info h3 {
    font-size: 1rem;
    color: var(--admin-gray);
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stat-change.positive {
    color: var(--admin-success);
}

.stat-change.negative {
    color: var(--admin-danger);
}

.dashboard-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.widget {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--admin-border);
}

.widget-header h2 {
    font-size: 1.25rem;
    color: var(--admin-dark);
}

.view-all {
    color: var(--admin-primary);
    text-decoration: none;
    font-size: 0.875rem;
}

.view-all:hover {
    text-decoration: underline;
}

.widget-content {
    padding: 1.5rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.activity-item:last-child {
    margin-bottom: 0;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    margin-left: 1rem;
    flex-shrink: 0;
}

.activity-info h4 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.activity-info p {
    font-size: 0.875rem;
    color: var(--admin-gray);
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--admin-gray-light);
}

.chart-filters {
    display: flex;
    gap: 0.5rem;
}

.chart-filters button {
    background: none;
    border: none;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    color: var(--admin-gray);
    cursor: pointer;
    border-radius: 0.25rem;
    transition: var(--admin-transition);
}

.chart-filters button:hover {
    background-color: var(--admin-gray-light);
}

.chart-filters button.active {
    background-color: var(--admin-primary);
    color: white;
}

.chart-placeholder {
    height: 250px;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 1rem 0;
    margin-bottom: 1rem;
}

.chart-bars {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    width: 100%;
    height: 100%;
}

.chart-bar {
    width: 40px;
    background-color: var(--admin-primary);
    border-radius: 4px 4px 0 0;
    position: relative;
    transition: var(--admin-transition);
    cursor: pointer;
}

.chart-bar:hover {
    background-color: var(--admin-secondary);
}

.bar-label {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    color: var(--admin-gray);
    white-space: nowrap;
}

.bar-tooltip {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--admin-dark);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 10;
}

.bar-tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: var(--admin-dark) transparent transparent transparent;
}

.chart-summary {
    display: flex;
    justify-content: space-between;
    border-top: 1px solid var(--admin-border);
    padding-top: 1rem;
}

.summary-item h4 {
    font-size: 0.875rem;
    color: var(--admin-gray);
    margin-bottom: 0.25rem;
}

.summary-item p {
    font-size: 1.25rem;
    font-weight: bold;
}

.dashboard-tables {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.data-table {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--admin-border);
}

.table-header h2 {
    font-size: 1.25rem;
    color: var(--admin-dark);
}

.table-content {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 1rem 1.5rem;
    text-align: right;
}

th {
    background-color: var(--admin-gray-light);
    font-weight: 600;
    color: var(--admin-gray-dark);
}

td {
    border-bottom: 1px solid var(--admin-border);
}

tr:last-child td {
    border-bottom: none;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-info img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-left: 0.75rem;
}

.status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status.active {
    background-color: rgba(76, 217, 100, 0.1);
    color: var(--admin-success);
}

.status.inactive {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--admin-gray);
}

.status.suspended {
    background-color: rgba(255, 59, 48, 0.1);
    color: var(--admin-danger);
}

/* Users Page Styles */
.filters-container {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.filters-wrapper {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-size: 0.875rem;
    color: var(--admin-gray);
    margin-bottom: 0.25rem;
}

.filter-group select {
    padding: 0.5rem;
    border: 1px solid var(--admin-border);
    border-radius: 0.25rem;
    font-family: 'Tajawal', sans-serif;
    min-width: 150px;
}

.search-wrapper {
    display: flex;
    gap: 0.5rem;
}

.search-wrapper input {
    padding: 0.5rem 1rem;
    border: 1px solid var(--admin-border);
    border-radius: 0.25rem;
    font-family: 'Tajawal', sans-serif;
    min-width: 250px;
}

.actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transition: var(--admin-transition);
}

.view-btn {
    background-color: rgba(90, 200, 250, 0.1);
    color: var(--admin-info);
}

.view-btn:hover {
    background-color: var(--admin-info);
    color: white;
}

.edit-btn {
    background-color: rgba(255, 149, 0, 0.1);
    color: var(--admin-warning);
}

.edit-btn:hover {
    background-color: var(--admin-warning);
    color: white;
}

.delete-btn {
    background-color: rgba(255, 59, 48, 0.1);
    color: var(--admin-danger);
}

.delete-btn:hover {
    background-color: var(--admin-danger);
    color: white;
}

.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
}

.pagination-info {
    color: var(--admin-gray);
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    gap: 0.25rem;
}

.pagination-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--admin-border);
    background-color: white;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: var(--admin-transition);
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--admin-gray-light);
}

.pagination-btn.active {
    background-color: var(--admin-primary);
    color: white;
    border-color: var(--admin-primary);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-options {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--admin-gray);
    font-size: 0.875rem;
}

.pagination-options select {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--admin-border);
    border-radius: 0.25rem;
    font-family: 'Tajawal', sans-serif;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1100;
    opacity: 0;
    visibility: hidden;
    transition: var(--admin-transition);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: white;
    border-radius: 0.5rem;
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--admin-shadow);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--admin-border);
}

.modal-header h2 {
    font-size: 1.25rem;
    color: var(--admin-dark);
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--admin-gray);
    cursor: pointer;
    transition: var(--admin-transition);
}

.close-modal:hover {
    color: var(--admin-danger);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--admin-border);
}

.user-form .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.user-form .form-group {
    flex: 1;
}

.user-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.user-form input,
.user-form select,
.user-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--admin-border);
    border-radius: 0.25rem;
    font-family: 'Tajawal', sans-serif;
}

.user-form textarea {
    min-height: 100px;
    resize: vertical;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .admin-login-container {
        flex-direction: column;
    }
    
    .admin-login-info {
        display: none;
    }
    
    .dashboard-widgets,
    .dashboard-tables {
        grid-template-columns: 1fr;
    }
    
    .user-form .form-row {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .admin-sidebar {
        width: var(--admin-sidebar-collapsed-width);
        transform: translateX(var(--admin-sidebar-collapsed-width));
    }
    
    .admin-main {
        margin-right: 0;
    }
    
    .admin-container.sidebar-collapsed .admin-sidebar {
        transform: translateX(0);
    }
    
    .sidebar-toggle {
        transform: rotate(180deg);
    }
    
    .admin-container.sidebar-collapsed .sidebar-toggle {
        transform: rotate(0);
    }
    
    .header-search {
        width: auto;
        flex: 1;
        margin-left: 1rem;
    }
    
    .filters-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filters-wrapper,
    .search-wrapper {
        width: 100%;
    }
    
    .pagination {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }
}
