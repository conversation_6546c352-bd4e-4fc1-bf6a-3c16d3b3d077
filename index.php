<?php
/**
 * الصفحة الرئيسية
 * تعرض الصفحة الرئيسية للموقع
 */

// استيراد ملف الإعدادات
require_once 'config.php';

// التحقق من حالة التثبيت
if (!defined('INSTALLED') || !INSTALLED) {
    // إعادة التوجيه إلى صفحة التثبيت
    header('Location: install_new.php');
    exit;
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once 'db_connection.php';

// الحصول على قوالب الدعوات
$templates = fetchAll("SELECT * FROM Templates WHERE status = 'active' AND is_public = 1 ORDER BY views DESC LIMIT 6");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - دعوات إلكترونية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6c63ff;
            --secondary-color: #4a42e8;
            --accent-color: #ff6b6b;
            --text-color: #333;
            --light-text: #666;
            --lighter-text: #999;
            --white: #fff;
            --light-bg: #f9f9f9;
            --border-color: #eee;
            --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Tajawal', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--light-bg);
            direction: rtl;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background-color: var(--white);
            box-shadow: var(--shadow);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
        }
        
        .nav-menu li {
            margin-right: 20px;
        }
        
        .nav-menu li:last-child {
            margin-right: 0;
        }
        
        .nav-menu a {
            color: var(--text-color);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }
        
        .nav-menu a:hover {
            color: var(--primary-color);
        }
        
        .auth-buttons {
            display: flex;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: 500;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
        }
        
        .btn-primary:hover {
            background-color: var(--secondary-color);
        }
        
        .btn-outline {
            background-color: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
            margin-left: 10px;
        }
        
        .btn-outline:hover {
            background-color: var(--primary-color);
            color: var(--white);
        }
        
        .hero {
            padding: 80px 0;
            background-color: var(--white);
            text-align: center;
        }
        
        .hero h1 {
            font-size: 48px;
            margin-bottom: 20px;
            color: var(--primary-color);
        }
        
        .hero p {
            font-size: 18px;
            color: var(--light-text);
            margin-bottom: 30px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .features {
            padding: 80px 0;
            background-color: var(--light-bg);
        }
        
        .section-title {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .section-title h2 {
            font-size: 36px;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .section-title p {
            color: var(--light-text);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .feature-card {
            background-color: var(--white);
            border-radius: 10px;
            padding: 30px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: var(--transition);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 48px;
            color: var(--primary-color);
            margin-bottom: 20px;
        }
        
        .feature-card h3 {
            font-size: 24px;
            margin-bottom: 15px;
        }
        
        .feature-card p {
            color: var(--light-text);
        }
        
        .templates {
            padding: 80px 0;
            background-color: var(--white);
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .template-card {
            background-color: var(--light-bg);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
        }
        
        .template-card:hover {
            transform: translateY(-5px);
        }
        
        .template-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .template-content {
            padding: 20px;
        }
        
        .template-content h3 {
            font-size: 20px;
            margin-bottom: 10px;
        }
        
        .template-content p {
            color: var(--light-text);
            margin-bottom: 15px;
        }
        
        .template-meta {
            display: flex;
            justify-content: space-between;
            color: var(--lighter-text);
            font-size: 14px;
        }
        
        .pricing {
            padding: 80px 0;
            background-color: var(--light-bg);
        }
        
        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .pricing-card {
            background-color: var(--white);
            border-radius: 10px;
            padding: 30px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: var(--transition);
        }
        
        .pricing-card:hover {
            transform: translateY(-5px);
        }
        
        .pricing-card.popular {
            border: 2px solid var(--primary-color);
            position: relative;
        }
        
        .popular-badge {
            position: absolute;
            top: 0;
            right: 20px;
            background-color: var(--primary-color);
            color: var(--white);
            padding: 5px 10px;
            font-size: 12px;
            border-radius: 0 0 5px 5px;
        }
        
        .pricing-card h3 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .price {
            font-size: 48px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 20px;
        }
        
        .price span {
            font-size: 16px;
            font-weight: 400;
            color: var(--light-text);
        }
        
        .pricing-features {
            list-style: none;
            margin-bottom: 30px;
        }
        
        .pricing-features li {
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .pricing-features li:last-child {
            border-bottom: none;
        }
        
        .cta {
            padding: 80px 0;
            background-color: var(--primary-color);
            color: var(--white);
            text-align: center;
        }
        
        .cta h2 {
            font-size: 36px;
            margin-bottom: 20px;
        }
        
        .cta p {
            font-size: 18px;
            margin-bottom: 30px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .btn-white {
            background-color: var(--white);
            color: var(--primary-color);
            border: none;
        }
        
        .btn-white:hover {
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        footer {
            background-color: var(--text-color);
            color: var(--white);
            padding: 50px 0 20px;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .footer-column h3 {
            font-size: 18px;
            margin-bottom: 20px;
            color: var(--white);
        }
        
        .footer-links {
            list-style: none;
        }
        
        .footer-links li {
            margin-bottom: 10px;
        }
        
        .footer-links a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition);
        }
        
        .footer-links a:hover {
            color: var(--white);
        }
        
        .social-links {
            display: flex;
            list-style: none;
        }
        
        .social-links li {
            margin-right: 15px;
        }
        
        .social-links a {
            color: rgba(255, 255, 255, 0.7);
            font-size: 20px;
            transition: var(--transition);
        }
        
        .social-links a:hover {
            color: var(--white);
        }
        
        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
            }
            
            .nav-menu {
                margin: 20px 0;
            }
            
            .hero h1 {
                font-size: 36px;
            }
            
            .hero p {
                font-size: 16px;
            }
            
            .section-title h2 {
                font-size: 30px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.php" class="logo"><?php echo SITE_NAME; ?></a>
                
                <ul class="nav-menu">
                    <li><a href="#features">المميزات</a></li>
                    <li><a href="#templates">القوالب</a></li>
                    <li><a href="#pricing">الأسعار</a></li>
                    <li><a href="#contact">اتصل بنا</a></li>
                </ul>
                
                <div class="auth-buttons">
                    <a href="login.php" class="btn btn-outline">تسجيل الدخول</a>
                    <a href="register.php" class="btn btn-primary">إنشاء حساب</a>
                </div>
            </div>
        </div>
    </header>
    
    <section class="hero">
        <div class="container">
            <h1>دعوات إلكترونية مميزة</h1>
            <p>أنشئ دعوات إلكترونية احترافية لمناسباتك الخاصة بسهولة وسرعة. اختر من بين مجموعة متنوعة من القوالب الجميلة وخصصها حسب رغبتك.</p>
            <a href="register.php" class="btn btn-primary">ابدأ الآن</a>
        </div>
    </section>
    
    <section class="features" id="features">
        <div class="container">
            <div class="section-title">
                <h2>مميزات الخدمة</h2>
                <p>اكتشف المميزات الرائعة التي تقدمها خدمتنا لإنشاء دعوات إلكترونية مميزة</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-paint-brush"></i>
                    </div>
                    <h3>تصاميم احترافية</h3>
                    <p>اختر من بين مجموعة متنوعة من التصاميم الاحترافية والجذابة لمناسبتك.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <h3>رموز QR فريدة</h3>
                    <p>إنشاء رمز QR فريد لكل مدعو لتسهيل عملية تسجيل الحضور.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>متوافق مع الجوال</h3>
                    <p>تصاميم متجاوبة تعمل بشكل مثالي على جميع الأجهزة والشاشات.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-share-alt"></i>
                    </div>
                    <h3>مشاركة سهلة</h3>
                    <p>مشاركة الدعوات بسهولة عبر وسائل التواصل الاجتماعي والبريد الإلكتروني والرسائل.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3>تأكيد الحضور</h3>
                    <p>إمكانية تأكيد الحضور إلكترونياً وتتبع الردود بسهولة.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h3>خرائط تفاعلية</h3>
                    <p>إضافة خرائط تفاعلية لمساعدة المدعوين في الوصول إلى موقع المناسبة.</p>
                </div>
            </div>
        </div>
    </section>
    
    <section class="templates" id="templates">
        <div class="container">
            <div class="section-title">
                <h2>قوالب مميزة</h2>
                <p>اختر من بين مجموعة متنوعة من القوالب المميزة لمناسبتك</p>
            </div>
            
            <div class="templates-grid">
                <?php if (empty($templates)): ?>
                    <p>لا توجد قوالب متاحة حالياً.</p>
                <?php else: ?>
                    <?php foreach ($templates as $template): ?>
                        <div class="template-card">
                            <img src="<?php echo htmlspecialchars($template['thumbnail']); ?>" alt="<?php echo htmlspecialchars($template['name']); ?>" class="template-image">
                            <div class="template-content">
                                <h3><?php echo htmlspecialchars($template['name']); ?></h3>
                                <p><?php echo htmlspecialchars(substr($template['description'], 0, 100)) . '...'; ?></p>
                                <div class="template-meta">
                                    <span><i class="fas fa-eye"></i> <?php echo $template['views']; ?></span>
                                    <span><i class="fas fa-star"></i> <?php echo $template['rating']; ?></span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="templates.php" class="btn btn-primary">عرض جميع القوالب</a>
            </div>
        </div>
    </section>
    
    <section class="pricing" id="pricing">
        <div class="container">
            <div class="section-title">
                <h2>باقات الأسعار</h2>
                <p>اختر الباقة المناسبة لاحتياجاتك</p>
            </div>
            
            <div class="pricing-grid">
                <div class="pricing-card">
                    <h3>الباقة الأساسية</h3>
                    <div class="price">79 <span>ريال</span></div>
                    <ul class="pricing-features">
                        <li>الارسال عبر الواتساب أو الايميل</li>
                        <li>تأكيد الحضور</li>
                        <li>كود لكل شخص</li>
                        <li>موقع المناسبة</li>
                        <li>استخدام تطبيق المسح الضوئي</li>
                        <li>5 دعوات كحد أقصى</li>
                        <li>100 مدعو لكل دعوة</li>
                    </ul>
                    <a href="register.php?package=basic" class="btn btn-outline">اشترك الآن</a>
                </div>
                
                <div class="pricing-card popular">
                    <div class="popular-badge">الأكثر شعبية</div>
                    <h3>الباقة المميزة</h3>
                    <div class="price">900 <span>ريال</span></div>
                    <ul class="pricing-features">
                        <li>جميع مميزات الباقة الأساسية</li>
                        <li>إدارة الدعوات</li>
                        <li>موظفة تنظيم دخول يوم الحفل</li>
                        <li>استبدال دعوات المعتذرين</li>
                        <li>إمكانية توفير حساب فرعي</li>
                        <li>عروض على التصاميم</li>
                        <li>20 دعوة كحد أقصى</li>
                        <li>500 مدعو لكل دعوة</li>
                    </ul>
                    <a href="register.php?package=premium" class="btn btn-primary">اشترك الآن</a>
                </div>
                
                <div class="pricing-card">
                    <h3>باقة البريميوم</h3>
                    <div class="price">5000 <span>ريال</span></div>
                    <ul class="pricing-features">
                        <li>جميع مميزات الباقة الأساسية</li>
                        <li>مدير حساب</li>
                        <li>إمكانية توفير رقم خاص</li>
                        <li>خيارات تخصيص الدعوة</li>
                        <li>100 دعوة كحد أقصى</li>
                        <li>1000 مدعو لكل دعوة</li>
                    </ul>
                    <a href="register.php?package=vip" class="btn btn-outline">اشترك الآن</a>
                </div>
            </div>
        </div>
    </section>
    
    <section class="cta">
        <div class="container">
            <h2>ابدأ في إنشاء دعواتك الإلكترونية الآن</h2>
            <p>انضم إلى آلاف المستخدمين الذين يستخدمون خدمتنا لإنشاء دعوات إلكترونية مميزة لمناسباتهم الخاصة.</p>
            <a href="register.php" class="btn btn-white">سجل الآن</a>
        </div>
    </section>
    
    <footer id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>روابط سريعة</h3>
                    <ul class="footer-links">
                        <li><a href="#features">المميزات</a></li>
                        <li><a href="#templates">القوالب</a></li>
                        <li><a href="#pricing">الأسعار</a></li>
                        <li><a href="login.php">تسجيل الدخول</a></li>
                        <li><a href="register.php">إنشاء حساب</a></li>
                    </ul>
                </div>
                
                <div class="footer-column">
                    <h3>الدعم</h3>
                    <ul class="footer-links">
                        <li><a href="faq.php">الأسئلة الشائعة</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="privacy.php">سياسة الخصوصية</a></li>
                        <li><a href="terms.php">شروط الاستخدام</a></li>
                    </ul>
                </div>
                
                <div class="footer-column">
                    <h3>تواصل معنا</h3>
                    <ul class="footer-links">
                        <li><i class="fas fa-envelope"></i> <?php echo ADMIN_EMAIL; ?></li>
                        <li><i class="fas fa-phone"></i> +966 50 123 4567</li>
                        <li><i class="fas fa-map-marker-alt"></i> الرياض، المملكة العربية السعودية</li>
                    </ul>
                    
                    <h3 style="margin-top: 20px;">تابعنا</h3>
                    <ul class="social-links">
                        <li><a href="#"><i class="fab fa-twitter"></i></a></li>
                        <li><a href="#"><i class="fab fa-facebook"></i></a></li>
                        <li><a href="#"><i class="fab fa-instagram"></i></a></li>
                        <li><a href="#"><i class="fab fa-linkedin"></i></a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>
</body>
</html>
