<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء دعوة جديدة | دعوات إلكترونية</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <!-- QR Code Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
    <!-- Barcode Library -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="logo">
                    <a href="index.html">
                        <div class="text-logo">دعوات إلكترونية</div>
                    </a>
                </div>
                <div class="nav-links">
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="index.html#features">المميزات</a></li>
                        <li><a href="index.html#how-it-works">كيف يعمل</a></li>
                        <li><a href="index.html#testimonials">العملاء</a></li>
                        <li><a href="index.html#pricing">الباقات والأسعار</a></li>
                        <li><a href="index.html#contact">تواصل معنا</a></li>
                    </ul>
                </div>
                <div class="auth-buttons">
                    <a href="login.html" class="btn btn-login">تسجيل الدخول</a>
                    <a href="admin/login.html" class="btn btn-secondary">لوحة التحكم</a>
                </div>
                <div class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Create Invitation Section -->
    <section class="create-invitation-section">
        <div class="container">
            <div class="page-header">
                <h1>إنشاء دعوة جديدة</h1>
                <p>قم بإنشاء دعوة مخصصة لمناسبتك الخاصة</p>
            </div>

            <!-- Progress Steps -->
            <div class="progress-steps">
                <div class="step active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-label">اختيار القالب</div>
                </div>
                <div class="step" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-label">تفاصيل المناسبة</div>
                </div>
                <div class="step" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-label">تخصيص التصميم</div>
                </div>
                <div class="step" data-step="4">
                    <div class="step-number">4</div>
                    <div class="step-label">المدعوين</div>
                </div>
                <div class="step" data-step="5">
                    <div class="step-number">5</div>
                    <div class="step-label">المعاينة والنشر</div>
                </div>
            </div>

            <!-- Form Container -->
            <div class="create-invitation-container">
                <!-- Step 1: Choose Template -->
                <div class="step-content active" id="step-1">
                    <h2>اختر قالب الدعوة</h2>

                    <div class="template-filters">
                        <div class="filter-group">
                            <label for="category-filter">نوع المناسبة</label>
                            <select id="category-filter">
                                <option value="all">الكل</option>
                                <option value="wedding" selected>زفاف</option>
                                <option value="birthday">عيد ميلاد</option>
                                <option value="graduation">تخرج</option>
                                <option value="conference">مؤتمر</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="style-filter">النمط</label>
                            <select id="style-filter">
                                <option value="all">الكل</option>
                                <option value="modern">عصري</option>
                                <option value="classic">كلاسيكي</option>
                                <option value="elegant" selected>أنيق</option>
                                <option value="simple">بسيط</option>
                                <option value="colorful">ملون</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="color-filter">اللون</label>
                            <select id="color-filter">
                                <option value="all">الكل</option>
                                <option value="blue">أزرق</option>
                                <option value="pink">وردي</option>
                                <option value="gold" selected>ذهبي</option>
                                <option value="green">أخضر</option>
                                <option value="purple">بنفسجي</option>
                                <option value="red">أحمر</option>
                            </select>
                        </div>
                    </div>

                    <div class="templates-grid">
                        <div class="template-card selected">
                            <div class="template-image">
                                <img src="images/templates/template1.jpg" alt="قالب زفاف أنيق">
                                <div class="template-overlay">
                                    <button class="btn btn-sm btn-primary preview-btn">معاينة</button>
                                </div>
                            </div>
                            <div class="template-info">
                                <h3>قالب زفاف أنيق</h3>
                                <div class="template-meta">
                                    <span class="template-category">زفاف</span>
                                    <span class="template-style">أنيق</span>
                                </div>
                            </div>
                            <div class="template-select">
                                <input type="radio" name="template" id="template1" value="template1" checked>
                                <label for="template1">اختيار</label>
                            </div>
                        </div>

                        <div class="template-card">
                            <div class="template-image">
                                <img src="images/templates/template2.jpg" alt="قالب زفاف كلاسيكي">
                                <div class="template-overlay">
                                    <button class="btn btn-sm btn-primary preview-btn">معاينة</button>
                                </div>
                            </div>
                            <div class="template-info">
                                <h3>قالب زفاف كلاسيكي</h3>
                                <div class="template-meta">
                                    <span class="template-category">زفاف</span>
                                    <span class="template-style">كلاسيكي</span>
                                </div>
                            </div>
                            <div class="template-select">
                                <input type="radio" name="template" id="template2" value="template2">
                                <label for="template2">اختيار</label>
                            </div>
                        </div>

                        <div class="template-card">
                            <div class="template-image">
                                <img src="images/templates/template3.jpg" alt="قالب زفاف ذهبي">
                                <div class="template-overlay">
                                    <button class="btn btn-sm btn-primary preview-btn">معاينة</button>
                                </div>
                            </div>
                            <div class="template-info">
                                <h3>قالب زفاف ذهبي</h3>
                                <div class="template-meta">
                                    <span class="template-category">زفاف</span>
                                    <span class="template-style">أنيق</span>
                                </div>
                            </div>
                            <div class="template-select">
                                <input type="radio" name="template" id="template3" value="template3">
                                <label for="template3">اختيار</label>
                            </div>
                        </div>

                        <div class="template-card">
                            <div class="template-image">
                                <img src="images/templates/template4.jpg" alt="قالب زفاف بسيط">
                                <div class="template-overlay">
                                    <button class="btn btn-sm btn-primary preview-btn">معاينة</button>
                                </div>
                            </div>
                            <div class="template-info">
                                <h3>قالب زفاف بسيط</h3>
                                <div class="template-meta">
                                    <span class="template-category">زفاف</span>
                                    <span class="template-style">بسيط</span>
                                </div>
                            </div>
                            <div class="template-select">
                                <input type="radio" name="template" id="template4" value="template4">
                                <label for="template4">اختيار</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Event Details -->
                <div class="step-content" id="step-2">
                    <h2>أدخل تفاصيل المناسبة</h2>

                    <div class="form-group">
                        <label for="event-title">عنوان المناسبة</label>
                        <input type="text" id="event-title" placeholder="مثال: حفل زفاف محمد وسارة">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="event-date">تاريخ المناسبة</label>
                            <input type="date" id="event-date">
                        </div>

                        <div class="form-group">
                            <label for="event-time">وقت المناسبة</label>
                            <input type="time" id="event-time">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="event-location">مكان المناسبة</label>
                        <input type="text" id="event-location" placeholder="مثال: قاعة الأميرات، الرياض">
                    </div>

                    <div class="form-group">
                        <label for="event-description">وصف المناسبة (اختياري)</label>
                        <textarea id="event-description" rows="4" placeholder="أضف وصفًا للمناسبة أو أي معلومات إضافية"></textarea>
                    </div>
                </div>

                <!-- Step 3: Design Customization -->
                <div class="step-content" id="step-3">
                    <h2>تخصيص التصميم</h2>

                    <div class="customization-container">
                        <div class="customization-sidebar">
                            <div class="customization-section">
                                <h3>الألوان</h3>
                                <div class="form-group">
                                    <label for="primary-color">اللون الرئيسي</label>
                                    <input type="color" id="primary-color" value="#6c63ff">
                                </div>

                                <div class="form-group">
                                    <label for="secondary-color">اللون الثانوي</label>
                                    <input type="color" id="secondary-color" value="#ff6b6b">
                                </div>
                            </div>

                            <div class="customization-section">
                                <h3>الخطوط</h3>
                                <div class="form-group">
                                    <label for="font-family">نوع الخط</label>
                                    <select id="font-family">
                                        <option value="Tajawal" selected>Tajawal</option>
                                        <option value="Cairo">Cairo</option>
                                        <option value="Amiri">Amiri</option>
                                        <option value="Changa">Changa</option>
                                    </select>
                                </div>
                            </div>

                            <div class="customization-section">
                                <h3>صورة الدعوة</h3>
                                <div class="form-group">
                                    <label for="invitation-image">إضافة صورة</label>
                                    <input type="file" id="invitation-image" accept="image/*" style="display: none;">
                                    <div class="image-preview-container"></div>
                                </div>
                            </div>

                            <div class="customization-section">
                                <h3>كود QR</h3>
                                <div class="qr-code-controls">
                                    <p>يمكنك تغيير لون وحجم كود QR وسحبه لتحديد موقعه على الدعوة</p>
                                </div>
                            </div>

                            <div class="customization-section">
                                <h3>الباركود</h3>
                                <div class="barcode-controls">
                                    <p>يمكنك تغيير نوع ولون وحجم الباركود وسحبه لتحديد موقعه على الدعوة</p>
                                    <div class="form-group">
                                        <label for="barcode-format">نوع الباركود</label>
                                        <select id="barcode-format">
                                            <option value="CODE128" selected>CODE128</option>
                                            <option value="CODE39">CODE39</option>
                                            <option value="EAN13">EAN13</option>
                                            <option value="UPC">UPC</option>
                                            <option value="EAN8">EAN8</option>
                                            <option value="ITF14">ITF14</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="barcode-color">لون الباركود</label>
                                        <input type="color" id="barcode-color" value="#000000">
                                    </div>
                                    <div class="form-group">
                                        <label for="barcode-width">عرض الباركود</label>
                                        <input type="range" id="barcode-width" min="1" max="4" step="0.5" value="2">
                                    </div>
                                    <div class="form-group">
                                        <label for="barcode-height">ارتفاع الباركود</label>
                                        <input type="range" id="barcode-height" min="50" max="150" step="10" value="100">
                                    </div>
                                    <div class="form-group">
                                        <label for="barcode-display-value">
                                            <input type="checkbox" id="barcode-display-value" checked>
                                            عرض القيمة
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="customization-preview">
                            <div class="template-customization-preview"></div>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Guest List -->
                <div class="step-content" id="step-4">
                    <h2>قائمة المدعوين</h2>

                    <div class="guest-list-container">
                        <div class="guest-list-actions">
                            <button class="btn btn-primary" id="add-guest-btn">
                                <i class="fas fa-plus"></i>
                                إضافة مدعو
                            </button>

                            <button class="btn btn-secondary" id="import-guests-btn">
                                <i class="fas fa-file-import"></i>
                                استيراد من ملف
                            </button>
                        </div>

                        <div class="guest-list">
                            <div class="guest-list-header">
                                <div class="guest-name">الاسم</div>
                                <div class="guest-phone">رقم الجوال</div>
                                <div class="guest-email">البريد الإلكتروني</div>
                                <div class="guest-actions">إجراءات</div>
                            </div>

                            <div class="guest-list-body">
                                <!-- Guest items will be added here -->
                                <div class="empty-guest-list">
                                    <i class="fas fa-users"></i>
                                    <p>لم تتم إضافة مدعوين بعد</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Preview and Publish -->
                <div class="step-content" id="step-5">
                    <h2>معاينة ونشر الدعوة</h2>

                    <div class="preview-publish-container">
                        <div class="final-preview">
                            <h3>معاينة الدعوة</h3>
                            <div class="final-preview-frame">
                                <!-- Preview iframe will be added here -->
                            </div>

                            <div class="preview-actions">
                                <button class="btn btn-secondary" id="preview-desktop-btn">
                                    <i class="fas fa-desktop"></i>
                                    عرض كمبيوتر
                                </button>

                                <button class="btn btn-secondary" id="preview-mobile-btn">
                                    <i class="fas fa-mobile-alt"></i>
                                    عرض جوال
                                </button>
                            </div>
                        </div>

                        <div class="publish-options">
                            <h3>خيارات النشر</h3>

                            <div class="form-group">
                                <label for="invitation-url">رابط الدعوة</label>
                                <div class="url-input-group">
                                    <input type="text" id="invitation-url" value="invitation-123456" readonly>
                                    <button class="btn btn-secondary" id="copy-url-btn">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                                <small>سيكون رابط دعوتك: https://example.com/invitation-123456</small>
                            </div>

                            <div class="form-group">
                                <label>خيارات المشاركة</label>
                                <div class="share-buttons">
                                    <button class="btn btn-whatsapp">
                                        <i class="fab fa-whatsapp"></i>
                                        واتساب
                                    </button>

                                    <button class="btn btn-email">
                                        <i class="fas fa-envelope"></i>
                                        بريد إلكتروني
                                    </button>

                                    <button class="btn btn-sms">
                                        <i class="fas fa-sms"></i>
                                        رسالة نصية
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="form-navigation">
                    <button class="btn btn-secondary" id="prev-btn" disabled>السابق</button>
                    <button class="btn btn-primary" id="next-btn">التالي</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <div class="text-logo">دعوات إلكترونية</div>
                    <p>شركة للاتصالات وتقنية المعلومات</p>
                    <p>الرياض - المملكة العربية السعودية</p>
                </div>
                <div class="footer-links">
                    <div class="footer-links-column">
                        <h3>روابط سريعة</h3>
                        <ul>
                            <li><a href="index.html">الرئيسية</a></li>
                            <li><a href="index.html#features">المميزات</a></li>
                            <li><a href="index.html#how-it-works">كيف يعمل</a></li>
                            <li><a href="index.html#testimonials">العملاء</a></li>
                            <li><a href="index.html#pricing">الباقات والأسعار</a></li>
                            <li><a href="index.html#contact">تواصل معنا</a></li>
                        </ul>
                    </div>
                    <div class="footer-links-column">
                        <h3>الشروط والأحكام</h3>
                        <ul>
                            <li><a href="terms.html">شروط الاستخدام</a></li>
                            <li><a href="privacy.html">سياسة الخصوصية</a></li>
                            <li><a href="faq.html">الأسئلة الشائعة</a></li>
                        </ul>
                    </div>
                </div>
                <div class="footer-social">
                    <h3>تواصل معنا</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-whatsapp"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© 2025 جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/qrcode.js"></script>
    <script src="js/barcode.js"></script>
    <script src="js/image-upload.js"></script>
    <script src="js/create-invitation.js"></script>
    <style>
        /* Create Invitation Styles */
        .create-invitation-section {
            padding: 60px 0;
            background-color: #f8f9fa;
            min-height: 80vh;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #333;
        }

        .page-header p {
            font-size: 1.1rem;
            color: #666;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
            position: relative;
        }

        .progress-steps::before {
            content: "";
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #e0e0e0;
            z-index: 1;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e0e0e0;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-bottom: 10px;
            transition: all 0.3s;
        }

        .step.active .step-number {
            background-color: var(--primary-color);
            color: white;
        }

        .step-label {
            font-size: 0.9rem;
            color: #666;
            transition: all 0.3s;
        }

        .step.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
        }

        .create-invitation-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-bottom: 40px;
        }

        .step-content {
            display: none;
        }

        .step-content.active {
            display: block;
        }

        .step-content h2 {
            font-size: 1.5rem;
            margin-bottom: 25px;
            color: #333;
            text-align: center;
        }

        .template-filters {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-group label {
            font-size: 0.9rem;
            color: #666;
        }

        .filter-group select {
            padding: 10px 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
            min-width: 150px;
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .template-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .template-card:hover {
            transform: translateY(-5px);
        }

        .template-card.selected {
            border-color: var(--primary-color);
        }

        .template-image {
            position: relative;
            height: 180px;
            overflow: hidden;
        }

        .template-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .template-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .template-image:hover .template-overlay {
            opacity: 1;
        }

        .template-info {
            padding: 15px;
        }

        .template-info h3 {
            font-size: 1.1rem;
            margin-bottom: 10px;
            color: #333;
        }

        .template-meta {
            display: flex;
            gap: 10px;
        }

        .template-category,
        .template-style {
            background-color: #f0f0f0;
            color: #666;
            padding: 3px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
        }

        .template-select {
            padding: 10px 15px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
        }

        .template-select input[type="radio"] {
            margin-left: 10px;
        }

        .form-navigation {
            display: flex;
            justify-content: space-between;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-group input[type="text"],
        .form-group input[type="date"],
        .form-group input[type="time"],
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
            font-size: 1rem;
        }

        .form-row {
            display: flex;
            gap: 20px;
        }

        .form-row .form-group {
            flex: 1;
        }

        /* Customization Styles */
        .customization-container {
            display: flex;
            gap: 30px;
            margin-top: 20px;
        }

        .customization-sidebar {
            width: 300px;
            flex-shrink: 0;
        }

        .customization-preview {
            flex-grow: 1;
            background-color: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
            min-height: 500px;
            position: relative;
        }

        .customization-section {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .customization-section h3 {
            font-size: 1.1rem;
            margin-bottom: 15px;
            color: #333;
        }

        .template-customization-preview {
            width: 100%;
            height: 100%;
            min-height: 500px;
            position: relative;
            overflow: hidden;
        }

        .template-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 1.2rem;
            color: #666;
        }

        .template-preview-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .template-preview-container img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* QR Code Styles */
        .qr-code-container {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 10;
            background-color: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .qr-drag-handle {
            position: absolute;
            top: -15px;
            right: -15px;
            width: 30px;
            height: 30px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: move;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        /* Barcode Styles */
        .barcode-container {
            position: absolute;
            top: 150px;
            left: 20px;
            z-index: 10;
            background-color: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .drag-handle {
            position: absolute;
            top: -15px;
            right: -15px;
            width: 30px;
            height: 30px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: move;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        /* Image Upload Styles */
        .image-preview-container {
            width: 100%;
            height: 150px;
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            overflow: hidden;
            position: relative;
            transition: all 0.3s;
        }

        .image-preview-container:hover {
            border-color: var(--primary-color);
        }

        .image-preview-container.dragover {
            border-color: var(--primary-color);
            background-color: rgba(108, 99, 255, 0.05);
        }

        .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #666;
        }

        .upload-placeholder i {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .remove-image-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            background-color: rgba(255, 255, 255, 0.8);
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #ff6b6b;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        /* Guest List Styles */
        .guest-list-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .guest-list-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .guest-list {
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            overflow: hidden;
        }

        .guest-list-header {
            display: grid;
            grid-template-columns: 2fr 1fr 2fr 1fr;
            background-color: #f8f9fa;
            padding: 15px;
            font-weight: 600;
            border-bottom: 1px solid #e0e0e0;
        }

        .guest-list-body {
            min-height: 200px;
        }

        .empty-guest-list {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 50px 0;
            color: #999;
        }

        .empty-guest-list i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #e0e0e0;
        }

        /* Preview and Publish Styles */
        .preview-publish-container {
            display: flex;
            gap: 30px;
        }

        .final-preview {
            flex: 2;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .publish-options {
            flex: 1;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .final-preview h3,
        .publish-options h3 {
            font-size: 1.2rem;
            margin-bottom: 20px;
            color: #333;
        }

        .final-preview-frame {
            width: 100%;
            height: 400px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .preview-actions {
            display: flex;
            gap: 15px;
        }

        .url-input-group {
            display: flex;
            margin-bottom: 5px;
        }

        .url-input-group input {
            flex-grow: 1;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .url-input-group button {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .share-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-whatsapp {
            background-color: #25D366;
            color: white;
        }

        .btn-email {
            background-color: #D44638;
            color: white;
        }

        .btn-sms {
            background-color: #3498db;
            color: white;
        }

        /* Responsive styles */
        @media (max-width: 992px) {
            .customization-container,
            .preview-publish-container {
                flex-direction: column;
            }

            .customization-sidebar {
                width: 100%;
            }
        }

        @media (max-width: 768px) {
            .template-filters {
                flex-direction: column;
                align-items: center;
            }

            .progress-steps {
                overflow-x: auto;
                padding-bottom: 15px;
            }

            .step {
                min-width: 100px;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .guest-list-header {
                grid-template-columns: 1fr 1fr;
            }

            .guest-list-header .guest-email {
                display: none;
            }
        }
    </style>
</body>
</html>
