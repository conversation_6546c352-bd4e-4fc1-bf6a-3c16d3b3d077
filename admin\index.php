<?php
/**
 * الصفحة الرئيسية للوحة التحكم
 */

// بدء الجلسة
session_start();

// استيراد ملف الإعدادات
require_once '../config.php';

// التحقق من حالة التثبيت
if (!defined('INSTALLED') || !INSTALLED) {
    // إعادة التوجيه إلى صفحة التثبيت
    header('Location: ../install_new.php');
    exit;
}

// استيراد ملف الاتصال بقاعدة البيانات
require_once '../db_connection.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    // إعادة التوجيه إلى صفحة تسجيل الدخول
    header('Location: login.php');
    exit;
}

// الحصول على إحصائيات الموقع
$stats = [
    'users' => fetchRow("SELECT COUNT(*) as count FROM Users WHERE role = 'user'")['count'] ?? 0,
    'invitations' => fetchRow("SELECT COUNT(*) as count FROM Invitations")['count'] ?? 0,
    'templates' => fetchRow("SELECT COUNT(*) as count FROM Templates")['count'] ?? 0,
    'guests' => fetchRow("SELECT COUNT(*) as count FROM Guests")['count'] ?? 0
];

// الحصول على آخر المستخدمين المسجلين
$latest_users = fetchAll("SELECT user_id, name, email, registration_date FROM Users ORDER BY registration_date DESC LIMIT 5");

// الحصول على آخر الدعوات
$latest_invitations = fetchAll("SELECT i.invitation_id, i.title, i.event_date, i.status, u.name as user_name 
                               FROM Invitations i 
                               JOIN Users u ON i.user_id = u.user_id 
                               ORDER BY i.created_at DESC LIMIT 5");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6c63ff;
            --secondary-color: #4a42e8;
            --accent-color: #ff6b6b;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --text-color: #333;
            --light-text: #666;
            --lighter-text: #999;
            --white: #fff;
            --light-bg: #f9f9f9;
            --dark-bg: #2c3e50;
            --border-color: #eee;
            --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Tajawal', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--light-bg);
            direction: rtl;
        }
        
        .dashboard {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background-color: var(--dark-bg);
            color: var(--white);
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .sidebar-header h2 {
            color: var(--white);
            margin-bottom: 5px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            margin-top: 15px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
        }
        
        .user-name {
            font-weight: 500;
        }
        
        .user-role {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-section {
            margin-bottom: 20px;
        }
        
        .menu-section-title {
            padding: 0 20px;
            font-size: 12px;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.5);
            margin-bottom: 10px;
        }
        
        .menu-items {
            list-style: none;
        }
        
        .menu-item {
            padding: 10px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition);
        }
        
        .menu-item:hover, .menu-item.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--white);
        }
        
        .menu-item i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            margin-right: 250px;
            padding: 20px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .page-title h1 {
            color: var(--primary-color);
        }
        
        .header-actions {
            display: flex;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 15px;
            border-radius: 5px;
            font-weight: 500;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            margin-right: 10px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
        }
        
        .btn-primary:hover {
            background-color: var(--secondary-color);
        }
        
        .btn-outline {
            background-color: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        
        .btn-outline:hover {
            background-color: var(--primary-color);
            color: var(--white);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background-color: var(--white);
            border-radius: 10px;
            padding: 20px;
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
        }
        
        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-left: 15px;
            color: var(--white);
        }
        
        .stat-icon.users {
            background-color: var(--primary-color);
        }
        
        .stat-icon.invitations {
            background-color: var(--success-color);
        }
        
        .stat-icon.templates {
            background-color: var(--warning-color);
        }
        
        .stat-icon.guests {
            background-color: var(--info-color);
        }
        
        .stat-info h3 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .stat-info p {
            color: var(--light-text);
            font-size: 14px;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        
        .card {
            background-color: var(--white);
            border-radius: 10px;
            padding: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 20px;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .card-action {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 14px;
            transition: var(--transition);
        }
        
        .card-action:hover {
            color: var(--secondary-color);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        table th, table td {
            padding: 10px;
            text-align: right;
        }
        
        table th {
            font-weight: 700;
            color: var(--light-text);
            border-bottom: 1px solid var(--border-color);
        }
        
        table tr:not(:last-child) td {
            border-bottom: 1px solid var(--border-color);
        }
        
        .status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 20px;
            font-size: 12px;
            color: var(--white);
        }
        
        .status.active {
            background-color: var(--success-color);
        }
        
        .status.draft {
            background-color: var(--warning-color);
        }
        
        .status.completed {
            background-color: var(--info-color);
        }
        
        .status.cancelled {
            background-color: var(--danger-color);
        }
        
        .date {
            color: var(--light-text);
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><?php echo SITE_NAME; ?></h2>
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <div class="user-name"><?php echo htmlspecialchars($_SESSION['user_name']); ?></div>
                        <div class="user-role">مدير</div>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-section">
                    <div class="menu-section-title">القائمة الرئيسية</div>
                    <ul class="menu-items">
                        <li><a href="index.php" class="menu-item active"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                        <li><a href="users.php" class="menu-item"><i class="fas fa-users"></i> المستخدمين</a></li>
                        <li><a href="invitations.php" class="menu-item"><i class="fas fa-envelope-open-text"></i> الدعوات</a></li>
                        <li><a href="templates.php" class="menu-item"><i class="fas fa-paint-brush"></i> القوالب</a></li>
                        <li><a href="packages.php" class="menu-item"><i class="fas fa-box"></i> الباقات</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <div class="menu-section-title">الإعدادات</div>
                    <ul class="menu-items">
                        <li><a href="profile.php" class="menu-item"><i class="fas fa-user-circle"></i> الملف الشخصي</a></li>
                        <li><a href="settings.php" class="menu-item"><i class="fas fa-cog"></i> إعدادات الموقع</a></li>
                        <li><a href="logout.php" class="menu-item"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="page-title">
                    <h1>لوحة التحكم</h1>
                    <p>مرحبًا بك في لوحة تحكم الموقع</p>
                </div>
                
                <div class="header-actions">
                    <a href="../index.php" class="btn btn-outline" target="_blank"><i class="fas fa-external-link-alt"></i> عرض الموقع</a>
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $stats['users']; ?></h3>
                        <p>المستخدمين</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon invitations">
                        <i class="fas fa-envelope-open-text"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $stats['invitations']; ?></h3>
                        <p>الدعوات</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon templates">
                        <i class="fas fa-paint-brush"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $stats['templates']; ?></h3>
                        <p>القوالب</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon guests">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $stats['guests']; ?></h3>
                        <p>المدعوين</p>
                    </div>
                </div>
            </div>
            
            <div class="content-grid">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">آخر المستخدمين المسجلين</div>
                        <a href="users.php" class="card-action">عرض الكل</a>
                    </div>
                    
                    <table>
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>تاريخ التسجيل</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($latest_users)): ?>
                                <tr>
                                    <td colspan="3" style="text-align: center;">لا يوجد مستخدمين مسجلين حتى الآن.</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($latest_users as $user): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($user['name']); ?></td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td class="date"><?php echo date('d/m/Y', strtotime($user['registration_date'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">آخر الدعوات</div>
                        <a href="invitations.php" class="card-action">عرض الكل</a>
                    </div>
                    
                    <table>
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>المستخدم</th>
                                <th>تاريخ المناسبة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($latest_invitations)): ?>
                                <tr>
                                    <td colspan="4" style="text-align: center;">لا توجد دعوات حتى الآن.</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($latest_invitations as $invitation): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($invitation['title']); ?></td>
                                        <td><?php echo htmlspecialchars($invitation['user_name']); ?></td>
                                        <td class="date"><?php echo date('d/m/Y', strtotime($invitation['event_date'])); ?></td>
                                        <td>
                                            <span class="status <?php echo $invitation['status']; ?>">
                                                <?php 
                                                    switch ($invitation['status']) {
                                                        case 'draft':
                                                            echo 'مسودة';
                                                            break;
                                                        case 'active':
                                                            echo 'نشط';
                                                            break;
                                                        case 'completed':
                                                            echo 'مكتمل';
                                                            break;
                                                        case 'cancelled':
                                                            echo 'ملغي';
                                                            break;
                                                        default:
                                                            echo $invitation['status'];
                                                    }
                                                ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
