<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأسئلة الشائعة | دعوات إلكترونية</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Tajawal is a good Arabic font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/legal.css">
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="logo">
                    <a href="index.html">
                        <div class="text-logo">دعوات إلكترونية</div>
                    </a>
                </div>
                <div class="nav-links">
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="index.html#features">المميزات</a></li>
                        <li><a href="index.html#how-it-works">كيف يعمل</a></li>
                        <li><a href="index.html#testimonials">العملاء</a></li>
                        <li><a href="index.html#pricing">الباقات والأسعار</a></li>
                        <li><a href="index.html#contact">تواصل معنا</a></li>
                    </ul>
                </div>
                <div class="auth-buttons">
                    <a href="login.html" class="btn btn-login">تسجيل الدخول</a>
                </div>
                <div class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- FAQ Section -->
    <section class="legal-section">
        <div class="container">
            <div class="faq-container">
                <div class="faq-header">
                    <h1>الأسئلة الشائعة</h1>
                    <p>إجابات على الأسئلة الأكثر شيوعاً</p>
                </div>
                
                <div class="faq-content">
                    <div class="faq-categories">
                        <button class="faq-category-btn active" data-category="general">عام</button>
                        <button class="faq-category-btn" data-category="account">الحساب</button>
                        <button class="faq-category-btn" data-category="invitations">الدعوات</button>
                        <button class="faq-category-btn" data-category="payment">الدفع</button>
                        <button class="faq-category-btn" data-category="technical">مشاكل تقنية</button>
                    </div>
                    
                    <div class="faq-list" id="general">
                        <!-- General Questions -->
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>ما هي خدمة "دعوات إلكترونية"؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>"دعوات إلكترونية" هي خدمة متكاملة لإنشاء وإدارة وإرسال الدعوات الإلكترونية للمناسبات المختلفة مثل حفلات الزفاف، أعياد الميلاد، المؤتمرات، وغيرها. تتميز الخدمة بإمكانية تأكيد الحضور، وإنشاء كود QR خاص لكل ضيف، وإضافة موقع المناسبة، والتواصل مع المدعوين.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>ما هي مميزات استخدام "دعوات إلكترونية" مقارنة بالدعوات التقليدية؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>تتميز "دعوات إلكترونية" بالعديد من المزايا مقارنة بالدعوات التقليدية:</p>
                                <ul>
                                    <li>توفير الوقت والجهد في إرسال الدعوات</li>
                                    <li>توفير التكاليف المرتبطة بطباعة وتوزيع الدعوات الورقية</li>
                                    <li>إمكانية تأكيد الحضور ومتابعة الردود بشكل آلي</li>
                                    <li>إمكانية إرسال تذكيرات قبل موعد المناسبة</li>
                                    <li>إضافة موقع المناسبة بسهولة</li>
                                    <li>كود QR خاص لكل ضيف للتحقق من الدعوات يوم المناسبة</li>
                                    <li>صديقة للبيئة (لا تستهلك ورق)</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>هل يمكنني تجربة الخدمة قبل الاشتراك؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>نعم، يمكنك تجربة الخدمة مجاناً من خلال صفحة "جرب مجاناً" على موقعنا. ستتمكن من إرسال دعوة تجريبية لنفسك لاختبار جميع مميزات الخدمة قبل الاشتراك.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="faq-list" id="account" style="display: none;">
                        <!-- Account Questions -->
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>كيف يمكنني إنشاء حساب؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>يمكنك إنشاء حساب بسهولة من خلال النقر على زر "تسجيل الدخول" في أعلى الصفحة، ثم اختيار "إنشاء حساب جديد". ستحتاج إلى إدخال بعض المعلومات الأساسية مثل الاسم، البريد الإلكتروني، ورقم الهاتف، ثم إنشاء كلمة مرور.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>نسيت كلمة المرور الخاصة بي، كيف يمكنني استعادتها؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>يمكنك استعادة كلمة المرور من خلال النقر على رابط "نسيت كلمة المرور؟" في صفحة تسجيل الدخول. سيتم إرسال رابط لإعادة تعيين كلمة المرور إلى بريدك الإلكتروني المسجل.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>كيف يمكنني تغيير معلومات حسابي؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>بعد تسجيل الدخول، يمكنك الوصول إلى إعدادات الحساب من خلال النقر على اسمك في أعلى الصفحة، ثم اختيار "إعدادات الحساب". هناك يمكنك تعديل معلوماتك الشخصية، وتغيير كلمة المرور، وتحديث تفضيلات الإشعارات.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="faq-list" id="invitations" style="display: none;">
                        <!-- Invitations Questions -->
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>كيف يمكنني إنشاء دعوة جديدة؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>بعد تسجيل الدخول، انتقل إلى لوحة التحكم واضغط على زر "إنشاء دعوة جديدة". ستظهر لك واجهة سهلة الاستخدام تتيح لك إدخال تفاصيل المناسبة، واختيار قالب التصميم، وإضافة قائمة المدعوين.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>هل يمكنني تخصيص تصميم الدعوة؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>نعم، نوفر مجموعة متنوعة من القوالب الجاهزة التي يمكنك الاختيار من بينها. كما يمكنك تخصيص الألوان، والخطوط، وإضافة صور خاصة بك. في الباقات المتقدمة، نوفر خيارات تخصيص إضافية وإمكانية تصميم دعوة مخصصة بالكامل.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>كيف يمكنني إضافة قائمة المدعوين؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>يمكنك إضافة المدعوين بعدة طرق:</p>
                                <ul>
                                    <li>إدخال المعلومات يدوياً (الاسم ورقم الهاتف)</li>
                                    <li>استيراد جهات الاتصال من ملف Excel</li>
                                    <li>استيراد جهات الاتصال من هاتفك (عبر التطبيق)</li>
                                </ul>
                                <p>بعد إضافة القائمة، يمكنك تنظيمها في مجموعات لتسهيل إدارتها.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="faq-list" id="payment" style="display: none;">
                        <!-- Payment Questions -->
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>ما هي خيارات الدفع المتاحة؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>نقبل مجموعة متنوعة من طرق الدفع، بما في ذلك:</p>
                                <ul>
                                    <li>بطاقات الائتمان (فيزا، ماستركارد)</li>
                                    <li>بطاقات مدى</li>
                                    <li>Apple Pay</li>
                                    <li>التحويل البنكي (للباقات المميزة والبريميوم)</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>هل يمكنني استرداد المبلغ بعد الدفع؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>وفقاً لسياستنا، جميع المدفوعات نهائية وغير قابلة للاسترداد بعد استخدام الخدمة. ومع ذلك، إذا واجهت مشكلة تقنية منعتك من استخدام الخدمة بشكل كامل، يرجى التواصل مع فريق الدعم للنظر في حالتك.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>هل هناك رسوم إضافية غير معلنة؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>لا، جميع الرسوم معلنة بوضوح في صفحة الباقات والأسعار. السعر المعروض يشمل جميع الميزات المذكورة في وصف الباقة. قد تكون هناك خدمات إضافية اختيارية بتكلفة إضافية، ولكن سيتم إعلامك بها قبل الشراء.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="faq-list" id="technical" style="display: none;">
                        <!-- Technical Questions -->
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>هل هناك تطبيق للهواتف الذكية؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>نعم، لدينا تطبيق متاح لأجهزة Android و iOS. يمكنك تحميله من Google Play أو App Store. يتيح لك التطبيق إدارة دعواتك، ومتابعة الردود، واستخدام ماسح الكود للتحقق من المدعوين يوم المناسبة.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>كيف يمكنني التحقق من المدعوين يوم المناسبة؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>يمكنك استخدام ميزة "المسح الضوئي للدعوات" في تطبيقنا. ببساطة افتح التطبيق، واختر المناسبة، ثم انقر على "مسح الكود". وجه الكاميرا نحو كود QR الخاص بالضيف، وسيتحقق النظام من صحة الدعوة ويسجل حضور الضيف تلقائياً.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">
                                <h3>ماذا يحدث إذا لم يستلم الضيف الدعوة؟</h3>
                                <div class="icon"><i class="fas fa-chevron-down"></i></div>
                            </div>
                            <div class="faq-answer">
                                <p>يمكنك متابعة حالة وصول الدعوات من خلال لوحة التحكم. إذا لم يستلم أحد الضيوف الدعوة، يمكنك إعادة إرسالها بنقرة واحدة. كما يمكنك تغيير طريقة الإرسال (مثلاً من WhatsApp إلى SMS أو البريد الإلكتروني) إذا كانت هناك مشكلة في الطريقة الأولى.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="faq-contact">
                        <h3>لم تجد إجابة لسؤالك؟</h3>
                        <p>فريق الدعم الفني متاح للإجابة على جميع استفساراتك</p>
                        <a href="index.html#contact" class="btn btn-primary">تواصل معنا</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <div class="text-logo">دعوات إلكترونية</div>
                    <p>شركة للاتصالات وتقنية المعلومات</p>
                    <p>الرياض - المملكة العربية السعودية</p>
                </div>
                <div class="footer-links">
                    <div class="footer-links-column">
                        <h3>روابط سريعة</h3>
                        <ul>
                            <li><a href="index.html">الرئيسية</a></li>
                            <li><a href="index.html#features">المميزات</a></li>
                            <li><a href="index.html#how-it-works">كيف يعمل</a></li>
                            <li><a href="index.html#testimonials">العملاء</a></li>
                            <li><a href="index.html#pricing">الباقات والأسعار</a></li>
                            <li><a href="index.html#contact">تواصل معنا</a></li>
                        </ul>
                    </div>
                    <div class="footer-links-column">
                        <h3>الشروط والأحكام</h3>
                        <ul>
                            <li><a href="terms.html">شروط الاستخدام</a></li>
                            <li><a href="privacy.html">سياسة الخصوصية</a></li>
                            <li><a href="faq.html" class="active">الأسئلة الشائعة</a></li>
                        </ul>
                    </div>
                </div>
                <div class="footer-social">
                    <h3>تواصل معنا</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-whatsapp"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© 2025 جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script>
        // FAQ Functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle FAQ answers
            const faqQuestions = document.querySelectorAll('.faq-question');
            
            faqQuestions.forEach(question => {
                question.addEventListener('click', function() {
                    const answer = this.nextElementSibling;
                    const isActive = this.classList.contains('active');
                    
                    // Close all other answers
                    document.querySelectorAll('.faq-question').forEach(q => {
                        q.classList.remove('active');
                        q.nextElementSibling.classList.remove('active');
                    });
                    
                    // Toggle current answer
                    if (!isActive) {
                        this.classList.add('active');
                        answer.classList.add('active');
                    }
                });
            });
            
            // Category switching
            const categoryButtons = document.querySelectorAll('.faq-category-btn');
            
            categoryButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    
                    // Hide all FAQ lists
                    document.querySelectorAll('.faq-list').forEach(list => {
                        list.style.display = 'none';
                    });
                    
                    // Show selected category
                    document.getElementById(category).style.display = 'block';
                    
                    // Update active button
                    categoryButtons.forEach(btn => {
                        btn.classList.remove('active');
                    });
                    this.classList.add('active');
                    
                    // Close all open questions
                    document.querySelectorAll('.faq-question').forEach(q => {
                        q.classList.remove('active');
                        q.nextElementSibling.classList.remove('active');
                    });
                });
            });
        });
    </script>
</body>
</html>
