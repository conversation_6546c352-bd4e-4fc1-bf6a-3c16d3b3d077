<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير | دعوات إلكترونية</title>
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/admin.css">
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <div class="admin-logo">
                    <div class="text-logo">دعوات إلكترونية</div>
                </div>
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="صورة المستخدم">
                </div>
                <div class="user-info">
                    <h3>أحمد محمد</h3>
                    <p>مدير النظام</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="index.html"><i class="fas fa-tachometer-alt"></i><span>لوحة التحكم</span></a></li>
                    <li><a href="users.html"><i class="fas fa-users"></i><span>المستخدمين</span></a></li>
                    <li><a href="invitations.html"><i class="fas fa-envelope-open-text"></i><span>الدعوات</span></a></li>
                    <li><a href="templates.html"><i class="fas fa-paint-brush"></i><span>قوالب التصميم</span></a></li>
                    <li><a href="packages.html"><i class="fas fa-box"></i><span>الباقات</span></a></li>
                    <li><a href="payments.html"><i class="fas fa-credit-card"></i><span>المدفوعات</span></a></li>
                    <li class="active"><a href="reports.html"><i class="fas fa-chart-bar"></i><span>التقارير</span></a></li>
                    <li><a href="settings.html"><i class="fas fa-cog"></i><span>الإعدادات</span></a></li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <a href="login.html" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="بحث...">
                </div>
                
                <div class="header-actions">
                    <div class="header-notification">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="header-message">
                        <i class="fas fa-envelope"></i>
                        <span class="message-badge">5</span>
                    </div>
                    <div class="header-profile">
                        <img src="images/admin-avatar.png" alt="صورة المستخدم">
                    </div>
                </div>
            </header>
            
            <div class="admin-content">
                <div class="page-header">
                    <h1>التقارير والإحصائيات</h1>
                    <div class="report-actions">
                        <div class="date-range">
                            <label for="date-range">الفترة الزمنية:</label>
                            <select id="date-range">
                                <option value="week">آخر أسبوع</option>
                                <option value="month" selected>آخر شهر</option>
                                <option value="quarter">آخر 3 أشهر</option>
                                <option value="year">آخر سنة</option>
                                <option value="custom">مخصص</option>
                            </select>
                        </div>
                        <button class="btn btn-secondary">
                            <i class="fas fa-download"></i>
                            تصدير التقرير
                        </button>
                    </div>
                </div>
                
                <!-- Reports Overview -->
                <div class="reports-overview">
                    <div class="overview-card">
                        <div class="overview-header">
                            <h3>إجمالي الإيرادات</h3>
                            <select class="period-selector">
                                <option value="daily">يومي</option>
                                <option value="weekly">أسبوعي</option>
                                <option value="monthly" selected>شهري</option>
                            </select>
                        </div>
                        <div class="overview-value">
                            <span class="value">45,920</span>
                            <span class="currency">ر.س</span>
                        </div>
                        <div class="overview-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>15% مقارنة بالفترة السابقة</span>
                        </div>
                        <div class="overview-chart">
                            <div class="chart-placeholder revenue-chart">
                                <div class="chart-bars">
                                    <div class="chart-bar" style="height: 30%;"></div>
                                    <div class="chart-bar" style="height: 50%;"></div>
                                    <div class="chart-bar" style="height: 70%;"></div>
                                    <div class="chart-bar" style="height: 60%;"></div>
                                    <div class="chart-bar" style="height: 80%;"></div>
                                    <div class="chart-bar" style="height: 90%;"></div>
                                    <div class="chart-bar" style="height: 40%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="overview-header">
                            <h3>المستخدمين الجدد</h3>
                            <select class="period-selector">
                                <option value="daily">يومي</option>
                                <option value="weekly">أسبوعي</option>
                                <option value="monthly" selected>شهري</option>
                            </select>
                        </div>
                        <div class="overview-value">
                            <span class="value">125</span>
                            <span class="unit">مستخدم</span>
                        </div>
                        <div class="overview-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>8% مقارنة بالفترة السابقة</span>
                        </div>
                        <div class="overview-chart">
                            <div class="chart-placeholder users-chart">
                                <div class="chart-line"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="overview-header">
                            <h3>الدعوات المرسلة</h3>
                            <select class="period-selector">
                                <option value="daily">يومي</option>
                                <option value="weekly">أسبوعي</option>
                                <option value="monthly" selected>شهري</option>
                            </select>
                        </div>
                        <div class="overview-value">
                            <span class="value">3,782</span>
                            <span class="unit">دعوة</span>
                        </div>
                        <div class="overview-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>23% مقارنة بالفترة السابقة</span>
                        </div>
                        <div class="overview-chart">
                            <div class="chart-placeholder invitations-chart">
                                <div class="chart-area"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="overview-header">
                            <h3>معدل تأكيد الحضور</h3>
                            <select class="period-selector">
                                <option value="daily">يومي</option>
                                <option value="weekly">أسبوعي</option>
                                <option value="monthly" selected>شهري</option>
                            </select>
                        </div>
                        <div class="overview-value">
                            <span class="value">72</span>
                            <span class="unit">%</span>
                        </div>
                        <div class="overview-change negative">
                            <i class="fas fa-arrow-down"></i>
                            <span>5% مقارنة بالفترة السابقة</span>
                        </div>
                        <div class="overview-chart">
                            <div class="chart-placeholder confirmation-chart">
                                <div class="chart-circle">
                                    <div class="chart-circle-inner">72%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Detailed Reports -->
                <div class="detailed-reports">
                    <div class="report-card">
                        <div class="report-header">
                            <h3>توزيع الإيرادات حسب الباقة</h3>
                            <div class="report-actions">
                                <button class="btn-icon">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn-icon">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                        <div class="report-content">
                            <div class="chart-placeholder pie-chart">
                                <div class="pie-segments">
                                    <div class="pie-segment" style="--percentage: 38%; --color: #6c63ff;"></div>
                                    <div class="pie-segment" style="--percentage: 45%; --color: #4cd964;"></div>
                                    <div class="pie-segment" style="--percentage: 17%; --color: #ff9500;"></div>
                                </div>
                            </div>
                            <div class="chart-legend">
                                <div class="legend-item">
                                    <div class="legend-color" style="background-color: #6c63ff;"></div>
                                    <div class="legend-text">
                                        <span class="legend-label">الباقة الأساسية</span>
                                        <span class="legend-value">38%</span>
                                    </div>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color" style="background-color: #4cd964;"></div>
                                    <div class="legend-text">
                                        <span class="legend-label">الباقة المميزة</span>
                                        <span class="legend-value">45%</span>
                                    </div>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color" style="background-color: #ff9500;"></div>
                                    <div class="legend-text">
                                        <span class="legend-label">باقة البريميوم</span>
                                        <span class="legend-value">17%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="report-card">
                        <div class="report-header">
                            <h3>توزيع المناسبات حسب النوع</h3>
                            <div class="report-actions">
                                <button class="btn-icon">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn-icon">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                        <div class="report-content">
                            <div class="chart-placeholder horizontal-bars">
                                <div class="h-bar-container">
                                    <div class="h-bar-label">زفاف</div>
                                    <div class="h-bar">
                                        <div class="h-bar-fill" style="width: 65%; background-color: #6c63ff;">65%</div>
                                    </div>
                                </div>
                                <div class="h-bar-container">
                                    <div class="h-bar-label">عيد ميلاد</div>
                                    <div class="h-bar">
                                        <div class="h-bar-fill" style="width: 15%; background-color: #4cd964;">15%</div>
                                    </div>
                                </div>
                                <div class="h-bar-container">
                                    <div class="h-bar-label">تخرج</div>
                                    <div class="h-bar">
                                        <div class="h-bar-fill" style="width: 10%; background-color: #ff9500;">10%</div>
                                    </div>
                                </div>
                                <div class="h-bar-container">
                                    <div class="h-bar-label">مؤتمر</div>
                                    <div class="h-bar">
                                        <div class="h-bar-fill" style="width: 5%; background-color: #ff3b30;">5%</div>
                                    </div>
                                </div>
                                <div class="h-bar-container">
                                    <div class="h-bar-label">أخرى</div>
                                    <div class="h-bar">
                                        <div class="h-bar-fill" style="width: 5%; background-color: #5ac8fa;">5%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="detailed-reports">
                    <div class="report-card full-width">
                        <div class="report-header">
                            <h3>أداء المبيعات الشهري</h3>
                            <div class="report-actions">
                                <div class="chart-filters">
                                    <button class="active">إيرادات</button>
                                    <button>عدد المعاملات</button>
                                </div>
                                <div class="report-buttons">
                                    <button class="btn-icon">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn-icon">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="report-content">
                            <div class="chart-placeholder area-chart">
                                <div class="area-chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="../js/main.js"></script>
    <style>
        /* Additional styles for reports page */
        .report-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .date-range {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .date-range label {
            font-weight: 500;
        }
        
        .date-range select {
            padding: 8px 12px;
            border: 1px solid var(--admin-border);
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
        }
        
        .reports-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .overview-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            padding: 20px;
        }
        
        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .overview-header h3 {
            font-size: 1rem;
            color: var(--admin-gray-dark);
        }
        
        .period-selector {
            padding: 5px;
            border: 1px solid var(--admin-border);
            border-radius: 5px;
            font-size: 0.8rem;
            font-family: 'Tajawal', sans-serif;
        }
        
        .overview-value {
            display: flex;
            align-items: baseline;
            margin-bottom: 10px;
        }
        
        .value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--admin-dark);
        }
        
        .currency, .unit {
            font-size: 1rem;
            color: var(--admin-gray);
            margin-right: 5px;
        }
        
        .overview-change {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
            margin-bottom: 20px;
        }
        
        .overview-change.positive {
            color: var(--admin-success);
        }
        
        .overview-change.negative {
            color: var(--admin-danger);
        }
        
        .chart-placeholder {
            height: 100px;
            position: relative;
        }
        
        .chart-bars {
            display: flex;
            align-items: flex-end;
            justify-content: space-between;
            height: 100%;
        }
        
        .chart-bar {
            width: 8px;
            background-color: var(--admin-primary);
            border-radius: 4px 4px 0 0;
        }
        
        .revenue-chart .chart-bar {
            background-color: var(--admin-primary);
        }
        
        .users-chart .chart-line {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to top right, transparent 49.5%, #4cd964 49.5%, #4cd964 50.5%, transparent 50.5%);
        }
        
        .invitations-chart .chart-area {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to top, rgba(255, 149, 0, 0.2) 0%, rgba(255, 149, 0, 0) 100%);
            clip-path: polygon(0% 100%, 10% 80%, 20% 90%, 30% 70%, 40% 80%, 50% 60%, 60% 70%, 70% 50%, 80% 60%, 90% 40%, 100% 50%, 100% 100%);
        }
        
        .confirmation-chart .chart-circle {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: conic-gradient(#ff3b30 0% 28%, #4cd964 28% 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .chart-circle-inner {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: var(--admin-dark);
        }
        
        .detailed-reports {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .report-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        .report-card.full-width {
            grid-column: 1 / -1;
        }
        
        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid var(--admin-border);
        }
        
        .report-header h3 {
            font-size: 1.1rem;
            color: var(--admin-dark);
        }
        
        .report-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            background-color: var(--admin-gray-light);
            color: var(--admin-gray);
            cursor: pointer;
            transition: var(--admin-transition);
        }
        
        .btn-icon:hover {
            background-color: var(--admin-primary);
            color: white;
        }
        
        .chart-filters {
            display: flex;
            gap: 5px;
        }
        
        .chart-filters button {
            padding: 5px 10px;
            border: none;
            background-color: var(--admin-gray-light);
            color: var(--admin-gray);
            border-radius: 5px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: var(--admin-transition);
        }
        
        .chart-filters button.active {
            background-color: var(--admin-primary);
            color: white;
        }
        
        .report-buttons {
            display: flex;
            gap: 5px;
            margin-right: 15px;
        }
        
        .report-content {
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .pie-chart {
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .pie-segments {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            position: relative;
            background-color: #f0f0f0;
            overflow: hidden;
        }
        
        .pie-segment {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: conic-gradient(var(--color) 0% var(--percentage), transparent var(--percentage) 100%);
        }
        
        .chart-legend {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
        }
        
        .legend-text {
            display: flex;
            justify-content: space-between;
            width: 100%;
        }
        
        .legend-label {
            font-size: 0.9rem;
            color: var(--admin-gray-dark);
        }
        
        .legend-value {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--admin-dark);
        }
        
        .horizontal-bars {
            height: 250px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .h-bar-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .h-bar-label {
            width: 80px;
            font-size: 0.9rem;
            color: var(--admin-gray-dark);
        }
        
        .h-bar {
            flex: 1;
            height: 20px;
            background-color: var(--admin-gray-light);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .h-bar-fill {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-left: 10px;
            color: white;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .area-chart {
            height: 300px;
        }
        
        .area-chart-container {
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(108, 99, 255, 0.2) 0%, rgba(108, 99, 255, 0) 100%);
            position: relative;
        }
        
        .area-chart-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--admin-primary);
        }
    </style>
</body>
</html>
