<?php
/**
 * ملف التنصيب
 * يستخدم لتنصيب الموقع وإنشاء قاعدة البيانات
 */

// بدء جلسة لحفظ بيانات التثبيت
session_start();

// التحقق من وجود ملف الإعدادات
if (!file_exists('config.php')) {
    die('ملف الإعدادات (config.php) غير موجود. يرجى التأكد من وجود الملف في المجلد الرئيسي.');
}

// استيراد ملف الإعدادات
require_once 'config.php';

// التحقق من حالة التثبيت
if (INSTALLED) {
    die('تم تثبيت الموقع بالفعل. إذا كنت ترغب في إعادة التثبيت، يرجى تعديل ملف الإعدادات (config.php) وتغيير قيمة INSTALLED إلى false.');
}

// تعريف المتغيرات
// استخدام الجلسة لحفظ الخطوة الحالية
$step = isset($_SESSION['install_step']) ? (int)$_SESSION['install_step'] : 1;

// إذا تم تحديد الخطوة في الـ URL، استخدمها بدلاً من الجلسة
if (isset($_GET['step'])) {
    $step = (int)$_GET['step'];
    $_SESSION['install_step'] = $step;
}

$error = '';
$success = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 1) {
        // التحقق من متطلبات النظام
        if (isset($_POST['check_requirements'])) {
            $step = 2;
            $_SESSION['install_step'] = $step;
        }
    } elseif ($step === 2) {
        // إعدادات قاعدة البيانات
        if (isset($_POST['db_settings'])) {
            $db_host = trim($_POST['db_host']);
            $db_user = trim($_POST['db_user']);
            $db_pass = trim($_POST['db_pass']);
            $db_name = trim($_POST['db_name']);
            
            // حفظ بيانات قاعدة البيانات في الجلسة
            $_SESSION['db_host'] = $db_host;
            $_SESSION['db_user'] = $db_user;
            $_SESSION['db_pass'] = $db_pass;
            $_SESSION['db_name'] = $db_name;
            
            // التحقق من صحة البيانات
            if (empty($db_host) || empty($db_user) || empty($db_name)) {
                $error = 'يرجى ملء جميع حقول إعدادات قاعدة البيانات.';
            } else {
                try {
                    // محاولة الاتصال بقاعدة البيانات
                    $conn = new mysqli($db_host, $db_user, $db_pass);
                    
                    if ($conn->connect_error) {
                        $error = 'فشل الاتصال بقاعدة البيانات: ' . $conn->connect_error;
                    } else {
                        // إنشاء قاعدة البيانات إذا لم تكن موجودة
                        $sql = "CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
                        
                        if ($conn->query($sql) === TRUE) {
                            // اختيار قاعدة البيانات
                            $conn->select_db($db_name);
                            
                            // تحديث ملف الإعدادات
                            $config_file = file_get_contents('config.php');
                            $config_file = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '$db_host');", $config_file);
                            $config_file = str_replace("define('DB_USER', '');", "define('DB_USER', '$db_user');", $config_file);
                            $config_file = str_replace("define('DB_PASS', '');", "define('DB_PASS', '$db_pass');", $config_file);
                            $config_file = str_replace("define('DB_NAME', '');", "define('DB_NAME', '$db_name');", $config_file);
                            
                            if (!file_put_contents('config.php', $config_file)) {
                                $error = 'فشل في كتابة ملف الإعدادات. تأكد من أن الملف قابل للكتابة.';
                            } else {
                                // التحقق من وجود ملف قاعدة البيانات
                                if (!file_exists('database.sql')) {
                                    $error = 'ملف قاعدة البيانات (database.sql) غير موجود.';
                                } else {
                                    // استيراد ملف قاعدة البيانات
                                    $sql_file = file_get_contents('database.sql');
                                    
                                    // تقسيم الملف إلى استعلامات منفصلة
                                    $queries = explode(';', $sql_file);
                                    
                                    // تنفيذ كل استعلام
                                    foreach ($queries as $query) {
                                        $query = trim($query);
                                        
                                        if (!empty($query)) {
                                            $conn->query($query);
                                            
                                            if ($conn->error) {
                                                $error = 'خطأ في تنفيذ الاستعلام: ' . $conn->error;
                                                break;
                                            }
                                        }
                                    }
                                    
                                    if (empty($error)) {
                                        $success = 'تم إنشاء قاعدة البيانات وتنفيذ الاستعلامات بنجاح.';
                                        $step = 3;
                                        $_SESSION['install_step'] = $step;
                                    }
                                }
                            }
                        } else {
                            $error = 'فشل إنشاء قاعدة البيانات: ' . $conn->error;
                        }
                        
                        $conn->close();
                    }
                } catch (Exception $e) {
                    $error = 'حدث خطأ: ' . $e->getMessage();
                }
            }
        }
    } elseif ($step === 3) {
        // إعدادات الموقع
        if (isset($_POST['site_settings'])) {
            $site_url = rtrim(trim($_POST['site_url']), '/');
            $admin_email = trim($_POST['admin_email']);
            $admin_name = trim($_POST['admin_name']);
            $admin_password = trim($_POST['admin_password']);
            $admin_password_confirm = trim($_POST['admin_password_confirm']);
            
            // حفظ بيانات الموقع في الجلسة
            $_SESSION['site_url'] = $site_url;
            $_SESSION['admin_email'] = $admin_email;
            $_SESSION['admin_name'] = $admin_name;
            
            // التحقق من صحة البيانات
            if (empty($site_url) || empty($admin_email) || empty($admin_name) || empty($admin_password)) {
                $error = 'يرجى ملء جميع حقول إعدادات الموقع.';
            } elseif ($admin_password !== $admin_password_confirm) {
                $error = 'كلمة المرور وتأكيد كلمة المرور غير متطابقين.';
            } elseif (!filter_var($admin_email, FILTER_VALIDATE_EMAIL)) {
                $error = 'البريد الإلكتروني غير صالح.';
            } else {
                try {
                    // تحديث ملف الإعدادات
                    $config_file = file_get_contents('config.php');
                    $config_file = str_replace("define('SITE_URL', '');", "define('SITE_URL', '$site_url');", $config_file);
                    $config_file = str_replace("define('ADMIN_EMAIL', '');", "define('ADMIN_EMAIL', '$admin_email');", $config_file);
                    $config_file = str_replace("define('MAIL_FROM', '');", "define('MAIL_FROM', '$admin_email');", $config_file);
                    
                    // تغيير حالة التثبيت إلى true
                    $config_file = str_replace("define('INSTALLED', false);", "define('INSTALLED', true);", $config_file);
                    
                    if (!file_put_contents('config.php', $config_file)) {
                        $error = 'فشل في كتابة ملف الإعدادات. تأكد من أن الملف قابل للكتابة.';
                    } else {
                        // تحديث بيانات المدير في قاعدة البيانات
                        if (!file_exists('db_connection.php')) {
                            $error = 'ملف الاتصال بقاعدة البيانات (db_connection.php) غير موجود.';
                        } else {
                            require_once 'db_connection.php';
                            
                            // تشفير كلمة المرور
                            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
                            
                            // تحديث بيانات المدير
                            $conn = connectDB();
                            
                            if ($conn) {
                                $stmt = $conn->prepare("UPDATE Users SET name = ?, email = ?, password = ? WHERE role = 'admin' LIMIT 1");
                                if ($stmt) {
                                    $stmt->bind_param("sss", $admin_name, $admin_email, $hashed_password);
                                    $stmt->execute();
                                    
                                    if ($stmt->affected_rows > 0 || $stmt->errno === 0) {
                                        $success = 'تم تثبيت الموقع بنجاح!';
                                        $step = 4;
                                        $_SESSION['install_step'] = $step;
                                        
                                        // تنظيف بيانات الجلسة بعد اكتمال التثبيت
                                        unset($_SESSION['db_host']);
                                        unset($_SESSION['db_user']);
                                        unset($_SESSION['db_pass']);
                                        unset($_SESSION['db_name']);
                                        unset($_SESSION['site_url']);
                                        unset($_SESSION['admin_email']);
                                        unset($_SESSION['admin_name']);
                                    } else {
                                        $error = 'فشل تحديث بيانات المدير: ' . $stmt->error;
                                    }
                                    
                                    $stmt->close();
                                } else {
                                    $error = 'فشل في إعداد الاستعلام: ' . $conn->error;
                                }
                                $conn->close();
                            } else {
                                $error = 'فشل الاتصال بقاعدة البيانات.';
                            }
                        }
                    }
                } catch (Exception $e) {
                    $error = 'حدث خطأ: ' . $e->getMessage();
                }
            }
        }
    }
}

// التحقق من متطلبات النظام
function checkRequirements() {
    $requirements = [
        'php_version' => [
            'name' => 'إصدار PHP',
            'required' => '7.4.0',
            'current' => PHP_VERSION,
            'status' => version_compare(PHP_VERSION, '7.4.0', '>=')
        ],
        'mysql_extension' => [
            'name' => 'امتداد MySQL',
            'required' => 'مثبت',
            'current' => extension_loaded('mysqli') ? 'مثبت' : 'غير مثبت',
            'status' => extension_loaded('mysqli')
        ],
        'pdo_extension' => [
            'name' => 'امتداد PDO',
            'required' => 'مثبت',
            'current' => extension_loaded('pdo') ? 'مثبت' : 'غير مثبت',
            'status' => extension_loaded('pdo')
        ],
        'gd_extension' => [
            'name' => 'امتداد GD',
            'required' => 'مثبت',
            'current' => extension_loaded('gd') ? 'مثبت' : 'غير مثبت',
            'status' => extension_loaded('gd')
        ],
        'config_writable' => [
            'name' => 'ملف الإعدادات قابل للكتابة',
            'required' => 'نعم',
            'current' => is_writable('config.php') ? 'نعم' : 'لا',
            'status' => is_writable('config.php')
        ],
        'uploads_writable' => [
            'name' => 'مجلد التحميلات قابل للكتابة',
            'required' => 'نعم',
            'current' => is_dir('uploads') && is_writable('uploads') ? 'نعم' : 'لا',
            'status' => is_dir('uploads') && is_writable('uploads')
        ],
        'qrcodes_writable' => [
            'name' => 'مجلد رموز QR قابل للكتابة',
            'required' => 'نعم',
            'current' => is_dir('qrcodes') && is_writable('qrcodes') ? 'نعم' : 'لا',
            'status' => is_dir('qrcodes') && is_writable('qrcodes')
        ]
    ];
    
    // إنشاء المجلدات إذا لم تكن موجودة
    if (!is_dir('uploads')) {
        @mkdir('uploads', 0755);
        $requirements['uploads_writable']['current'] = is_writable('uploads') ? 'نعم' : 'لا';
        $requirements['uploads_writable']['status'] = is_writable('uploads');
    }
    
    if (!is_dir('qrcodes')) {
        @mkdir('qrcodes', 0755);
        $requirements['qrcodes_writable']['current'] = is_writable('qrcodes') ? 'نعم' : 'لا';
        $requirements['qrcodes_writable']['status'] = is_writable('qrcodes');
    }
    
    return $requirements;
}

// التحقق من استيفاء جميع المتطلبات
function allRequirementsMet($requirements) {
    foreach ($requirements as $requirement) {
        if (!$requirement['status']) {
            return false;
        }
    }
    
    return true;
}

// الحصول على متطلبات النظام
$requirements = checkRequirements();
$all_requirements_met = allRequirementsMet($requirements);

// عرض الصفحة
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت موقع دعوات إلكترونية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6c63ff;
            --secondary-color: #4a42e8;
            --accent-color: #ff6b6b;
            --text-color: #333;
            --light-text: #666;
            --lighter-text: #999;
            --white: #fff;
            --light-bg: #f9f9f9;
            --border-color: #eee;
            --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Tajawal', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            background-color: var(--light-bg);
            direction: rtl;
        }
        
        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .install-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .install-header h1 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .install-content {
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--shadow);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .install-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            position: relative;
        }
        
        .install-steps::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--border-color);
            z-index: 1;
        }
        
        .step {
            position: relative;
            z-index: 2;
            text-align: center;
            width: 25%;
        }
        
        .step-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            background-color: var(--light-bg);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            margin: 0 auto 10px;
            color: var(--light-text);
            font-weight: bold;
        }
        
        .step.active .step-number {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--white);
        }
        
        .step.completed .step-number {
            background-color: #4CAF50;
            border-color: #4CAF50;
            color: var(--white);
        }
        
        .step-title {
            font-size: 14px;
            color: var(--light-text);
        }
        
        .step.active .step-title {
            color: var(--primary-color);
            font-weight: bold;
        }
        
        .step.completed .step-title {
            color: #4CAF50;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input[type="text"],
        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
            font-size: 16px;
        }
        
        button {
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-family: 'Tajawal', sans-serif;
            font-size: 16px;
            cursor: pointer;
            transition: var(--transition);
        }
        
        button:hover {
            background-color: var(--secondary-color);
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        table th,
        table td {
            padding: 10px;
            border: 1px solid