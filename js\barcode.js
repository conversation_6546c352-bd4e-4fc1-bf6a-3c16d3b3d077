/**
 * Barcode Generation and Customization Module
 * This module handles barcode generation and positioning on invitations
 */

// Barcode Generator using JsBarcode library
const BarcodeGenerator = {
    /**
     * Generate a barcode and append it to a container
     * @param {string} data - The data to encode in the barcode
     * @param {HTMLElement} container - The container to append the barcode to
     * @param {Object} options - Options for the barcode
     */
    generate: function(data, container, options = {}) {
        // Default options
        const defaultOptions = {
            format: "CODE128",
            width: 2,
            height: 100,
            displayValue: true,
            fontOptions: "",
            font: "monospace",
            textAlign: "center",
            textPosition: "bottom",
            textMargin: 2,
            fontSize: 20,
            background: "#ffffff",
            lineColor: "#000000",
            margin: 10
        };
        
        // Merge default options with provided options
        const barcodeOptions = { ...defaultOptions, ...options };
        
        // Clear the container
        container.innerHTML = '';
        
        // Create SVG element for the barcode
        const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
        container.appendChild(svg);
        
        // Generate the barcode
        JsBarcode(svg, data, barcodeOptions);
        
        // Set data attributes for position
        container.setAttribute('data-position-x', container.style.left || 0);
        container.setAttribute('data-position-y', container.style.top || 0);
        
        return container;
    },
    
    /**
     * Position a barcode on an invitation template
     * @param {HTMLElement} barcodeElement - The barcode element
     * @param {Object} position - The position object with x, y coordinates
     * @param {HTMLElement} templateContainer - The template container
     */
    positionOnTemplate: function(barcodeElement, position, templateContainer) {
        // Set the position of the barcode
        barcodeElement.style.position = 'absolute';
        barcodeElement.style.top = `${position.y}px`;
        barcodeElement.style.left = `${position.x}px`;
        
        // Make the barcode draggable
        this.makeDraggable(barcodeElement, templateContainer);
    },
    
    /**
     * Make an element draggable within a container
     * @param {HTMLElement} element - The element to make draggable
     * @param {HTMLElement} container - The container to constrain dragging within
     */
    makeDraggable: function(element, container) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
        
        // Add a drag handle to the element
        const dragHandle = document.createElement('div');
        dragHandle.className = 'drag-handle';
        dragHandle.innerHTML = '<i class="fas fa-arrows-alt"></i>';
        element.appendChild(dragHandle);
        
        // Mouse down event on the drag handle
        dragHandle.onmousedown = dragMouseDown;
        
        function dragMouseDown(e) {
            e = e || window.event;
            e.preventDefault();
            
            // Get the mouse cursor position at startup
            pos3 = e.clientX;
            pos4 = e.clientY;
            
            document.onmouseup = closeDragElement;
            document.onmousemove = elementDrag;
        }
        
        function elementDrag(e) {
            e = e || window.event;
            e.preventDefault();
            
            // Calculate the new cursor position
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            
            // Calculate the new position
            let newTop = element.offsetTop - pos2;
            let newLeft = element.offsetLeft - pos1;
            
            // Constrain to container
            const containerRect = container.getBoundingClientRect();
            const elementRect = element.getBoundingClientRect();
            
            if (newTop < 0) newTop = 0;
            if (newLeft < 0) newLeft = 0;
            if (newTop + elementRect.height > containerRect.height) {
                newTop = containerRect.height - elementRect.height;
            }
            if (newLeft + elementRect.width > containerRect.width) {
                newLeft = containerRect.width - elementRect.width;
            }
            
            // Set the element's new position
            element.style.top = newTop + "px";
            element.style.left = newLeft + "px";
            
            // Update the position data attribute for saving
            element.setAttribute('data-position-x', newLeft);
            element.setAttribute('data-position-y', newTop);
        }
        
        function closeDragElement() {
            // Stop moving when mouse button is released
            document.onmouseup = null;
            document.onmousemove = null;
        }
    },
    
    /**
     * Get the current position of a barcode
     * @param {HTMLElement} barcodeElement - The barcode element
     * @returns {Object} - The position object with x, y coordinates
     */
    getPosition: function(barcodeElement) {
        return {
            x: parseInt(barcodeElement.getAttribute('data-position-x') || 0),
            y: parseInt(barcodeElement.getAttribute('data-position-y') || 0)
        };
    }
};

// Export the barcode generator
window.BarcodeGenerator = BarcodeGenerator;
