<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات | دعوات إلكترونية</title>
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/admin.css">
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <div class="admin-logo">
                    <div class="text-logo">دعوات إلكترونية</div>
                </div>
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="صورة المستخدم">
                </div>
                <div class="user-info">
                    <h3>أحمد محمد</h3>
                    <p>مدير النظام</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="index.html"><i class="fas fa-tachometer-alt"></i><span>لوحة التحكم</span></a></li>
                    <li><a href="users.html"><i class="fas fa-users"></i><span>المستخدمين</span></a></li>
                    <li><a href="invitations.html"><i class="fas fa-envelope-open-text"></i><span>الدعوات</span></a></li>
                    <li><a href="templates.html"><i class="fas fa-paint-brush"></i><span>قوالب التصميم</span></a></li>
                    <li><a href="packages.html"><i class="fas fa-box"></i><span>الباقات</span></a></li>
                    <li><a href="payments.html"><i class="fas fa-credit-card"></i><span>المدفوعات</span></a></li>
                    <li><a href="reports.html"><i class="fas fa-chart-bar"></i><span>التقارير</span></a></li>
                    <li class="active"><a href="settings.html"><i class="fas fa-cog"></i><span>الإعدادات</span></a></li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <a href="login.html" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="بحث...">
                </div>
                
                <div class="header-actions">
                    <div class="header-notification">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="header-message">
                        <i class="fas fa-envelope"></i>
                        <span class="message-badge">5</span>
                    </div>
                    <div class="header-profile">
                        <img src="images/admin-avatar.png" alt="صورة المستخدم">
                    </div>
                </div>
            </header>
            
            <div class="admin-content">
                <div class="page-header">
                    <h1>إعدادات النظام</h1>
                    <button class="btn btn-primary save-settings-btn">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                </div>
                
                <!-- Settings Tabs -->
                <div class="settings-container">
                    <div class="settings-tabs">
                        <button class="tab-btn active" data-tab="general">
                            <i class="fas fa-sliders-h"></i>
                            إعدادات عامة
                        </button>
                        <button class="tab-btn" data-tab="email">
                            <i class="fas fa-envelope"></i>
                            البريد الإلكتروني
                        </button>
                        <button class="tab-btn" data-tab="payment">
                            <i class="fas fa-credit-card"></i>
                            بوابات الدفع
                        </button>
                        <button class="tab-btn" data-tab="security">
                            <i class="fas fa-shield-alt"></i>
                            الأمان والخصوصية
                        </button>
                        <button class="tab-btn" data-tab="notifications">
                            <i class="fas fa-bell"></i>
                            الإشعارات
                        </button>
                        <button class="tab-btn" data-tab="api">
                            <i class="fas fa-code"></i>
                            واجهة برمجة التطبيقات
                        </button>
                        <button class="tab-btn" data-tab="backup">
                            <i class="fas fa-database"></i>
                            النسخ الاحتياطي
                        </button>
                    </div>
                    
                    <div class="settings-content">
                        <!-- General Settings Tab -->
                        <div class="tab-content active" id="general">
                            <h2>الإعدادات العامة</h2>
                            
                            <div class="settings-section">
                                <h3>معلومات الموقع</h3>
                                
                                <div class="form-group">
                                    <label for="site-name">اسم الموقع</label>
                                    <input type="text" id="site-name" value="دعوات إلكترونية">
                                </div>
                                
                                <div class="form-group">
                                    <label for="site-description">وصف الموقع</label>
                                    <textarea id="site-description" rows="3">منصة متكاملة لإنشاء وإدارة الدعوات الإلكترونية لمختلف المناسبات</textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label for="site-logo">شعار الموقع</label>
                                    <div class="file-upload">
                                        <div class="file-preview">
                                            <img src="images/logo.png" alt="شعار الموقع">
                                        </div>
                                        <div class="file-actions">
                                            <button class="btn btn-secondary">تغيير الشعار</button>
                                            <button class="btn btn-text">حذف</button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="site-favicon">أيقونة الموقع (Favicon)</label>
                                    <div class="file-upload">
                                        <div class="file-preview favicon-preview">
                                            <img src="images/favicon.png" alt="أيقونة الموقع">
                                        </div>
                                        <div class="file-actions">
                                            <button class="btn btn-secondary">تغيير الأيقونة</button>
                                            <button class="btn btn-text">حذف</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="settings-section">
                                <h3>معلومات الاتصال</h3>
                                
                                <div class="form-group">
                                    <label for="contact-email">البريد الإلكتروني</label>
                                    <input type="email" id="contact-email" value="<EMAIL>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="contact-phone">رقم الهاتف</label>
                                    <input type="tel" id="contact-phone" value="+966 50 123 4567">
                                </div>
                                
                                <div class="form-group">
                                    <label for="contact-address">العنوان</label>
                                    <textarea id="contact-address" rows="3">الرياض، المملكة العربية السعودية</textarea>
                                </div>
                            </div>
                            
                            <div class="settings-section">
                                <h3>وسائل التواصل الاجتماعي</h3>
                                
                                <div class="form-group">
                                    <label for="social-facebook">فيسبوك</label>
                                    <div class="input-with-icon">
                                        <i class="fab fa-facebook-f"></i>
                                        <input type="text" id="social-facebook" value="https://facebook.com/einvitations">
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="social-twitter">تويتر</label>
                                    <div class="input-with-icon">
                                        <i class="fab fa-twitter"></i>
                                        <input type="text" id="social-twitter" value="https://twitter.com/einvitations">
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="social-instagram">انستغرام</label>
                                    <div class="input-with-icon">
                                        <i class="fab fa-instagram"></i>
                                        <input type="text" id="social-instagram" value="https://instagram.com/einvitations">
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="social-whatsapp">واتساب</label>
                                    <div class="input-with-icon">
                                        <i class="fab fa-whatsapp"></i>
                                        <input type="text" id="social-whatsapp" value="+966 50 123 4567">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="settings-section">
                                <h3>إعدادات اللغة والمنطقة</h3>
                                
                                <div class="form-group">
                                    <label for="default-language">اللغة الافتراضية</label>
                                    <select id="default-language">
                                        <option value="ar" selected>العربية</option>
                                        <option value="en">الإنجليزية</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="timezone">المنطقة الزمنية</label>
                                    <select id="timezone">
                                        <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                                        <option value="Asia/Dubai">دبي (GMT+4)</option>
                                        <option value="Europe/London">لندن (GMT+0)</option>
                                        <option value="America/New_York">نيويورك (GMT-5)</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="date-format">تنسيق التاريخ</label>
                                    <select id="date-format">
                                        <option value="dd/mm/yyyy" selected>DD/MM/YYYY</option>
                                        <option value="mm/dd/yyyy">MM/DD/YYYY</option>
                                        <option value="yyyy-mm-dd">YYYY-MM-DD</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="time-format">تنسيق الوقت</label>
                                    <select id="time-format">
                                        <option value="12" selected>12 ساعة (AM/PM)</option>
                                        <option value="24">24 ساعة</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Email Settings Tab -->
                        <div class="tab-content" id="email">
                            <h2>إعدادات البريد الإلكتروني</h2>
                            
                            <div class="settings-section">
                                <h3>إعدادات SMTP</h3>
                                
                                <div class="form-group">
                                    <label for="smtp-host">خادم SMTP</label>
                                    <input type="text" id="smtp-host" value="smtp.gmail.com">
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="smtp-port">منفذ SMTP</label>
                                        <input type="text" id="smtp-port" value="587">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="smtp-encryption">التشفير</label>
                                        <select id="smtp-encryption">
                                            <option value="tls" selected>TLS</option>
                                            <option value="ssl">SSL</option>
                                            <option value="none">بدون تشفير</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="smtp-username">اسم المستخدم</label>
                                    <input type="text" id="smtp-username" value="<EMAIL>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="smtp-password">كلمة المرور</label>
                                    <div class="input-with-icon">
                                        <i class="fas fa-lock"></i>
                                        <input type="password" id="smtp-password" value="••••••••••••">
                                        <button class="toggle-password">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="smtp-from-name">اسم المرسل</label>
                                    <input type="text" id="smtp-from-name" value="دعوات إلكترونية">
                                </div>
                                
                                <div class="form-group">
                                    <label for="smtp-from-email">بريد المرسل</label>
                                    <input type="email" id="smtp-from-email" value="<EMAIL>">
                                </div>
                                
                                <div class="form-actions">
                                    <button class="btn btn-secondary">اختبار الاتصال</button>
                                </div>
                            </div>
                            
                            <div class="settings-section">
                                <h3>قوالب البريد الإلكتروني</h3>
                                
                                <div class="email-templates">
                                    <div class="template-item">
                                        <div class="template-info">
                                            <h4>ترحيب المستخدم الجديد</h4>
                                            <p>يتم إرساله عند تسجيل مستخدم جديد</p>
                                        </div>
                                        <div class="template-actions">
                                            <button class="btn btn-secondary">تعديل</button>
                                        </div>
                                    </div>
                                    
                                    <div class="template-item">
                                        <div class="template-info">
                                            <h4>إعادة تعيين كلمة المرور</h4>
                                            <p>يتم إرساله عند طلب إعادة تعيين كلمة المرور</p>
                                        </div>
                                        <div class="template-actions">
                                            <button class="btn btn-secondary">تعديل</button>
                                        </div>
                                    </div>
                                    
                                    <div class="template-item">
                                        <div class="template-info">
                                            <h4>تأكيد الدفع</h4>
                                            <p>يتم إرساله بعد إتمام عملية الدفع بنجاح</p>
                                        </div>
                                        <div class="template-actions">
                                            <button class="btn btn-secondary">تعديل</button>
                                        </div>
                                    </div>
                                    
                                    <div class="template-item">
                                        <div class="template-info">
                                            <h4>إرسال دعوة</h4>
                                            <p>قالب الدعوة المرسلة للمدعوين</p>
                                        </div>
                                        <div class="template-actions">
                                            <button class="btn btn-secondary">تعديل</button>
                                        </div>
                                    </div>
                                    
                                    <div class="template-item">
                                        <div class="template-info">
                                            <h4>تذكير بالمناسبة</h4>
                                            <p>يتم إرساله قبل موعد المناسبة للتذكير</p>
                                        </div>
                                        <div class="template-actions">
                                            <button class="btn btn-secondary">تعديل</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="../js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle sidebar
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            const adminContainer = document.querySelector('.admin-container');
            
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    adminContainer.classList.toggle('sidebar-collapsed');
                });
            }
            
            // Settings tabs
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));
                    
                    // Add active class to clicked button
                    this.classList.add('active');
                    
                    // Show corresponding content
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });
            
            // Toggle password visibility
            const togglePassword = document.querySelector('.toggle-password');
            
            if (togglePassword) {
                togglePassword.addEventListener('click', function() {
                    const passwordInput = document.getElementById('smtp-password');
                    const icon = this.querySelector('i');
                    
                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        passwordInput.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            }
            
            // Save settings
            const saveButton = document.querySelector('.save-settings-btn');
            
            if (saveButton) {
                saveButton.addEventListener('click', function() {
                    // Show success message
                    const adminContent = document.querySelector('.admin-content');
                    
                    const successMessage = document.createElement('div');
                    successMessage.className = 'settings-success-message';
                    successMessage.innerHTML = `
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="success-text">
                            <h3>تم حفظ الإعدادات بنجاح</h3>
                            <p>تم تطبيق التغييرات بنجاح على النظام</p>
                        </div>
                    `;
                    
                    adminContent.appendChild(successMessage);
                    
                    // Remove message after 3 seconds
                    setTimeout(() => {
                        successMessage.classList.add('fade-out');
                        setTimeout(() => {
                            adminContent.removeChild(successMessage);
                        }, 300);
                    }, 3000);
                });
            }
        });
    </script>
    <style>
        /* Additional styles for settings page */
        .settings-container {
            display: flex;
            gap: 30px;
            margin-top: 30px;
        }
        
        .settings-tabs {
            width: 250px;
            flex-shrink: 0;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        .tab-btn {
            width: 100%;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            border: none;
            background-color: transparent;
            text-align: right;
            font-family: 'Tajawal', sans-serif;
            font-size: 1rem;
            color: var(--admin-gray-dark);
            cursor: pointer;
            transition: var(--admin-transition);
            border-right: 3px solid transparent;
        }
        
        .tab-btn:hover {
            background-color: var(--admin-gray-light);
        }
        
        .tab-btn.active {
            background-color: rgba(108, 99, 255, 0.1);
            color: var(--admin-primary);
            border-right-color: var(--admin-primary);
            font-weight: 500;
        }
        
        .tab-btn i {
            font-size: 1.2rem;
            width: 20px;
            text-align: center;
        }
        
        .settings-content {
            flex: 1;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            padding: 30px;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .tab-content h2 {
            font-size: 1.5rem;
            color: var(--admin-dark);
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--admin-border);
        }
        
        .settings-section {
            margin-bottom: 40px;
        }
        
        .settings-section h3 {
            font-size: 1.2rem;
            color: var(--admin-dark);
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--admin-gray-dark);
            margin-bottom: 8px;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--admin-border);
            border-radius: 5px;
            font-family: 'Tajawal', sans-serif;
            font-size: 1rem;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .input-with-icon {
            position: relative;
        }
        
        .input-with-icon i {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--admin-gray);
        }
        
        .input-with-icon input {
            padding-right: 35px;
        }
        
        .toggle-password {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--admin-gray);
            cursor: pointer;
        }
        
        .file-upload {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .file-preview {
            width: 100px;
            height: 100px;
            border-radius: 10px;
            overflow: hidden;
            background-color: var(--admin-gray-light);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .file-preview img {
            max-width: 100%;
            max-height: 100%;
        }
        
        .favicon-preview {
            width: 50px;
            height: 50px;
        }
        
        .file-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .form-actions {
            margin-top: 20px;
        }
        
        .btn-text {
            background: none;
            border: none;
            color: var(--admin-gray);
            cursor: pointer;
            padding: 5px 0;
            font-family: 'Tajawal', sans-serif;
            transition: var(--admin-transition);
        }
        
        .btn-text:hover {
            color: var(--admin-danger);
        }
        
        .email-templates {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .template-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background-color: var(--admin-gray-light);
            border-radius: 5px;
        }
        
        .template-info h4 {
            font-size: 1rem;
            color: var(--admin-dark);
            margin-bottom: 5px;
        }
        
        .template-info p {
            font-size: 0.9rem;
            color: var(--admin-gray);
        }
        
        .settings-success-message {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            z-index: 1000;
            animation: slide-in 0.3s ease;
        }
        
        .settings-success-message.fade-out {
            animation: slide-out 0.3s ease;
        }
        
        .success-icon {
            font-size: 2rem;
            color: var(--admin-success);
        }
        
        .success-text h3 {
            font-size: 1.1rem;
            color: var(--admin-dark);
            margin-bottom: 5px;
        }
        
        .success-text p {
            font-size: 0.9rem;
            color: var(--admin-gray);
        }
        
        @keyframes slide-in {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slide-out {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(-100%);
                opacity: 0;
            }
        }
    </style>
</body>
</html>
