<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر القوالب | لوحة التحكم</title>
    <link rel="stylesheet" href="styles/admin.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Tajawal is a good Arabic font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <!-- CodeMirror for code editing -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/closetag.min.js"></script>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <div class="admin-logo">
                    <div class="text-logo">دعوات إلكترونية</div>
                </div>
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="صورة المستخدم">
                </div>
                <div class="user-info">
                    <h3>أحمد محمد</h3>
                    <p>مدير النظام</p>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="index.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>المستخدمين</span>
                        </a>
                    </li>
                    <li>
                        <a href="invitations.html">
                            <i class="fas fa-envelope-open-text"></i>
                            <span>الدعوات</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="templates.html">
                            <i class="fas fa-paint-brush"></i>
                            <span>قوالب التصميم</span>
                        </a>
                    </li>
                    <li>
                        <a href="packages.html">
                            <i class="fas fa-box"></i>
                            <span>الباقات</span>
                        </a>
                    </li>
                    <li>
                        <a href="payments.html">
                            <i class="fas fa-credit-card"></i>
                            <span>المدفوعات</span>
                        </a>
                    </li>
                    <li>
                        <a href="reports.html">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <a href="login.html" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="بحث...">
                </div>
                
                <div class="header-actions">
                    <div class="header-notification">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="header-message">
                        <i class="fas fa-envelope"></i>
                        <span class="message-badge">5</span>
                    </div>
                    <div class="header-profile">
                        <img src="images/admin-avatar.png" alt="صورة المستخدم">
                    </div>
                </div>
            </header>
            
            <div class="admin-content">
                <div class="page-header">
                    <h1>إنشاء قالب جديد</h1>
                    <div class="page-actions">
                        <button class="btn btn-secondary" id="preview-template-btn">
                            <i class="fas fa-eye"></i>
                            معاينة
                        </button>
                        <button class="btn btn-primary" id="save-template-btn">
                            <i class="fas fa-save"></i>
                            حفظ القالب
                        </button>
                    </div>
                </div>
                
                <!-- Template Editor -->
                <div class="template-editor">
                    <div class="template-editor-container">
                        <div class="template-editor-sidebar">
                            <div class="template-info-section">
                                <h3>معلومات القالب</h3>
                                <form id="template-form">
                                    <div class="form-group">
                                        <label for="template-name">اسم القالب</label>
                                        <input type="text" id="template-name" placeholder="أدخل اسم القالب">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="template-category">التصنيف</label>
                                        <select id="template-category">
                                            <option value="wedding">زفاف</option>
                                            <option value="birthday">عيد ميلاد</option>
                                            <option value="graduation">تخرج</option>
                                            <option value="conference">مؤتمر</option>
                                            <option value="other">أخرى</option>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="template-style">النمط</label>
                                        <select id="template-style">
                                            <option value="modern">عصري</option>
                                            <option value="classic">كلاسيكي</option>
                                            <option value="elegant">أنيق</option>
                                            <option value="simple">بسيط</option>
                                            <option value="colorful">ملون</option>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="template-color">اللون الرئيسي</label>
                                        <select id="template-color">
                                            <option value="blue">أزرق</option>
                                            <option value="pink">وردي</option>
                                            <option value="gold">ذهبي</option>
                                            <option value="green">أخضر</option>
                                            <option value="purple">بنفسجي</option>
                                            <option value="red">أحمر</option>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="template-description">وصف القالب</label>
                                        <textarea id="template-description" rows="4" placeholder="أدخل وصفًا للقالب"></textarea>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="template-thumbnail">صورة مصغرة</label>
                                        <input type="file" id="template-thumbnail" accept="image/*">
                                        <div class="thumbnail-preview"></div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <div class="template-editor-content">
                            <div class="template-code-editor">
                                <div class="code-tabs">
                                    <button class="code-tab active" data-tab="html">HTML</button>
                                    <button class="code-tab" data-tab="css">CSS</button>
                                    <button class="code-tab" data-tab="js">JavaScript</button>
                                </div>
                                
                                <div class="code-editors">
                                    <div class="code-editor active" id="html-editor">
                                        <textarea id="template-html" placeholder="أدخل كود HTML"></textarea>
                                    </div>
                                    
                                    <div class="code-editor" id="css-editor">
                                        <textarea id="template-css" placeholder="أدخل كود CSS"></textarea>
                                    </div>
                                    
                                    <div class="code-editor" id="js-editor">
                                        <textarea id="template-js" placeholder="أدخل كود JavaScript"></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="template-preview">
                                <h3>معاينة القالب</h3>
                                <div class="template-preview-frame">
                                    <iframe frameborder="0"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="../js/main.js"></script>
    <script src="../js/template-editor.js"></script>
    <style>
        /* Template Editor Styles */
        .template-editor {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
        }
        
        .template-editor-container {
            display: flex;
            min-height: 700px;
        }
        
        .template-editor-sidebar {
            width: 300px;
            flex-shrink: 0;
            border-right: 1px solid #e0e0e0;
            padding: 20px;
            overflow-y: auto;
        }
        
        .template-editor-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        
        .template-info-section {
            margin-bottom: 30px;
        }
        
        .template-info-section h3 {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: #333;
        }
        
        .template-code-editor {
            flex-grow: 1;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }
        
        .code-tabs {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .code-tab {
            padding: 10px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-family: 'Tajawal', sans-serif;
            font-size: 1rem;
            color: #666;
            border-bottom: 2px solid transparent;
        }
        
        .code-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            font-weight: 600;
        }
        
        .code-editors {
            flex-grow: 1;
            position: relative;
        }
        
        .code-editor {
            display: none;
            height: 100%;
        }
        
        .code-editor.active {
            display: block;
        }
        
        .code-editor textarea {
            width: 100%;
            height: 300px;
            font-family: monospace;
            padding: 10px;
            border: none;
            resize: none;
        }
        
        .template-preview {
            padding: 20px;
        }
        
        .template-preview h3 {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: #333;
        }
        
        .template-preview-frame {
            width: 100%;
            height: 400px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .template-preview-frame iframe {
            width: 100%;
            height: 100%;
        }
        
        .thumbnail-preview {
            width: 100%;
            height: 150px;
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
            overflow: hidden;
        }
        
        .thumbnail-preview img {
            max-width: 100%;
            max-height: 100%;
        }
        
        /* CodeMirror Customizations */
        .CodeMirror {
            height: 300px;
            font-family: monospace;
            font-size: 14px;
        }
        
        /* Responsive styles */
        @media (max-width: 992px) {
            .template-editor-container {
                flex-direction: column;
            }
            
            .template-editor-sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #e0e0e0;
            }
        }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Code tabs functionality
            const codeTabs = document.querySelectorAll('.code-tab');
            const codeEditors = document.querySelectorAll('.code-editor');
            
            codeTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs and editors
                    codeTabs.forEach(t => t.classList.remove('active'));
                    codeEditors.forEach(e => e.classList.remove('active'));
                    
                    // Add active class to clicked tab and corresponding editor
                    this.classList.add('active');
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(`${tabId}-editor`).classList.add('active');
                });
            });
            
            // Thumbnail preview functionality
            const thumbnailInput = document.getElementById('template-thumbnail');
            const thumbnailPreview = document.querySelector('.thumbnail-preview');
            
            if (thumbnailInput && thumbnailPreview) {
                thumbnailInput.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        const reader = new FileReader();
                        
                        reader.onload = function(e) {
                            thumbnailPreview.innerHTML = `<img src="${e.target.result}" alt="صورة مصغرة">`;
                        };
                        
                        reader.readAsDataURL(this.files[0]);
                    }
                });
            }
        });
    </script>
</body>
</html>
