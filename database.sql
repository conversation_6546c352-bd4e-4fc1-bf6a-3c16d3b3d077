-- إنشاء قاعدة البيانات
-- CREATE DATABASE IF NOT EXISTS e_invitations CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE e_invitations;

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS Users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    phone VARCHAR(20),
    password VARCHAR(255) NOT NULL,
    address VARCHAR(255),
    role ENUM('user', 'admin') DEFAULT 'user',
    profile_image VARCHAR(255),
    registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الباقات
CREATE TABLE IF NOT EXISTS Packages (
    package_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    duration INT NOT NULL, -- بالأيام
    features TEXT,
    max_invitations INT,
    max_guests_per_invitation INT,
    status ENUM('active', 'inactive') DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول اشتراكات المستخدمين
CREATE TABLE IF NOT EXISTS Subscriptions (
    subscription_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    package_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (package_id) REFERENCES Packages(package_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول قوالب التصميم
CREATE TABLE IF NOT EXISTS Templates (
    template_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL, -- زفاف، عيد ميلاد، تخرج، مؤتمر، إلخ
    style VARCHAR(50), -- أنيق، كلاسيكي، عصري، بسيط، ملون
    color VARCHAR(50),
    thumbnail VARCHAR(255),
    html_content TEXT,
    css_content TEXT,
    js_content TEXT,
    created_by INT, -- المستخدم الذي أنشأ القالب (إذا كان مخصصًا)
    is_public BOOLEAN DEFAULT TRUE,
    views INT DEFAULT 0,
    downloads INT DEFAULT 0,
    rating DECIMAL(3, 1) DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    FOREIGN KEY (created_by) REFERENCES Users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الدعوات
CREATE TABLE IF NOT EXISTS Invitations (
    invitation_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    template_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    event_type VARCHAR(50) NOT NULL, -- زفاف، عيد ميلاد، تخرج، مؤتمر، إلخ
    event_date DATE NOT NULL,
    event_time TIME NOT NULL,
    location VARCHAR(255),
    location_coordinates VARCHAR(100), -- إحداثيات الموقع
    description TEXT,
    custom_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('draft', 'active', 'completed', 'cancelled') DEFAULT 'draft',
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES Templates(template_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المدعوين
CREATE TABLE IF NOT EXISTS Guests (
    guest_id INT PRIMARY KEY AUTO_INCREMENT,
    invitation_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    qr_code VARCHAR(255), -- مسار ملف QR code
    confirmation_status ENUM('pending', 'confirmed', 'declined') DEFAULT 'pending',
    confirmation_date DATETIME,
    attendance_status ENUM('pending', 'attended', 'absent') DEFAULT 'pending',
    attendance_date DATETIME,
    notes TEXT,
    FOREIGN KEY (invitation_id) REFERENCES Invitations(invitation_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الصور
CREATE TABLE IF NOT EXISTS Images (
    image_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    invitation_id INT,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_size INT,
    file_type VARCHAR(50),
    upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (invitation_id) REFERENCES Invitations(invitation_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول رسائل التواصل
CREATE TABLE IF NOT EXISTS Messages (
    message_id INT PRIMARY KEY AUTO_INCREMENT,
    guest_id INT,
    invitation_id INT NOT NULL,
    message_type ENUM('question', 'apology', 'other') NOT NULL,
    content TEXT NOT NULL,
    sent_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_read BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (guest_id) REFERENCES Guests(guest_id) ON DELETE SET NULL,
    FOREIGN KEY (invitation_id) REFERENCES Invitations(invitation_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المدفوعات
CREATE TABLE IF NOT EXISTS Payments (
    payment_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    subscription_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    payment_method VARCHAR(50),
    transaction_id VARCHAR(100),
    payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES Subscriptions(subscription_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول التقييمات
CREATE TABLE IF NOT EXISTS Ratings (
    rating_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    template_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating BETWEEN 1 AND 5),
    comment TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES Templates(template_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدخال بيانات أولية للباقات
INSERT INTO Packages (name, description, price, duration, features, max_invitations, max_guests_per_invitation, status)
VALUES 
('الباقة الأساسية', 'باقة أساسية تتضمن المميزات الرئيسية للدعوات الإلكترونية', 79.00, 30, 'الارسال عبر الواتساب أو الايميل,تأكيد الحضور,كود لكل شخص,موقع المناسبة,استخدام تطبيق المسح الضوئي', 5, 100, 'active'),
('الباقة المميزة', 'باقة متوسطة تتضمن مميزات إضافية للدعوات الإلكترونية', 900.00, 90, 'جميع مميزات الباقة الأساسية,إدارة الدعوات,موظفة تنظيم دخول يوم الحفل,استبدال دعوات المعتذرين,إمكانية توفير حساب فرعي,عروض على التصاميم', 20, 500, 'active'),
('باقة البريميوم', 'باقة متقدمة تتضمن جميع المميزات المتاحة', 5000.00, 365, 'جميع مميزات الباقة الأساسية,مدير حساب,إمكانية توفير رقم خاص,خيارات تخصيص الدعوة', 100, 1000, 'active');

-- إدخال بيانات أولية للمستخدمين (كلمة المرور: admin123)
INSERT INTO Users (name, email, phone, password, role, status)
VALUES ('أحمد محمد', '<EMAIL>', '+966505278757', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active');

-- إدخال بيانات أولية للقوالب
INSERT INTO Templates (name, description, category, style, color, thumbnail, is_public, views, downloads, rating, status)
VALUES 
('قالب زفاف أنيق', 'قالب أنيق لحفلات الزفاف مع تصميم عصري وألوان ذهبية فاخرة.', 'زفاف', 'أنيق', 'ذهبي', 'images/templates/template1.jpg', TRUE, 1250, 450, 4.8, 'active'),
('قالب عيد ميلاد ملون', 'قالب ملون لحفلات أعياد الميلاد مع تصميم مرح وألوان زاهية.', 'عيد ميلاد', 'ملون', 'متعدد', 'images/templates/template2.jpg', TRUE, 980, 320, 4.5, 'active'),
('قالب تخرج عصري', 'قالب عصري لحفلات التخرج مع تصميم أنيق وألوان مميزة.', 'تخرج', 'عصري', 'أزرق', 'images/templates/template3.jpg', TRUE, 750, 210, 4.2, 'active'),
('قالب مؤتمر بسيط', 'قالب بسيط للمؤتمرات والفعاليات الرسمية.', 'مؤتمر', 'بسيط', 'أبيض', 'images/templates/template4.jpg', TRUE, 620, 180, 4.0, 'active');
