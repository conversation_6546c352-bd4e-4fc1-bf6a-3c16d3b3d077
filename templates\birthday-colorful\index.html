<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دعوة عيد ميلاد ملونة</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap');
        
        :root {
            --primary-color: #ff6b6b;
            --secondary-color: #4ecdc4;
            --accent-color: #ffd166;
            --text-color: #333;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: var(--light-color);
            color: var(--text-color);
            line-height: 1.6;
        }
        
        .invitation-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .confetti {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .confetti-piece {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: var(--primary-color);
            top: -20px;
            animation: confetti-fall 3s linear infinite;
        }
        
        .confetti-piece:nth-child(2n) {
            background-color: var(--secondary-color);
            width: 15px;
            height: 15px;
            animation-delay: 0.5s;
            animation-duration: 3.5s;
        }
        
        .confetti-piece:nth-child(3n) {
            background-color: var(--accent-color);
            width: 8px;
            height: 8px;
            animation-delay: 1s;
            animation-duration: 4s;
        }
        
        .confetti-piece:nth-child(4n) {
            background-color: #a3a1ff;
            width: 12px;
            height: 12px;
            animation-delay: 1.5s;
            animation-duration: 4.5s;
        }
        
        @keyframes confetti-fall {
            0% {
                transform: translateY(0) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }
        
        .invitation-header {
            background-color: var(--primary-color);
            color: white;
            padding: 40px 20px;
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .invitation-title {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 800;
        }
        
        .invitation-subtitle {
            font-size: 1.5rem;
            font-weight: 300;
            margin-bottom: 20px;
        }
        
        .birthday-person {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--accent-color);
        }
        
        .invitation-body {
            padding: 40px 20px;
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .invitation-message {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.8;
        }
        
        .invitation-details {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .detail-item {
            flex: 1;
            min-width: 200px;
            padding: 20px;
            background-color: var(--light-color);
            border-radius: 10px;
            text-align: center;
        }
        
        .detail-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .detail-title {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .detail-text {
            font-size: 1rem;
        }
        
        .invitation-footer {
            background-color: var(--primary-color);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .rsvp-button {
            display: inline-block;
            padding: 12px 30px;
            background-color: var(--accent-color);
            color: var(--dark-color);
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            margin-top: 20px;
            transition: all 0.3s;
        }
        
        .rsvp-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .balloon {
            position: absolute;
            width: 60px;
            height: 70px;
            background-color: var(--primary-color);
            border-radius: 50%;
            animation: float 5s ease-in-out infinite;
            z-index: 1;
        }
        
        .balloon::before {
            content: '';
            position: absolute;
            width: 10px;
            height: 20px;
            background-color: var(--primary-color);
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 0 0 5px 5px;
        }
        
        .balloon::after {
            content: '';
            position: absolute;
            width: 2px;
            height: 50px;
            background-color: #ccc;
            bottom: -60px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .balloon:nth-child(2) {
            background-color: var(--secondary-color);
            right: 10%;
            top: 20%;
            animation-delay: 1s;
        }
        
        .balloon:nth-child(2)::before {
            background-color: var(--secondary-color);
        }
        
        .balloon:nth-child(3) {
            background-color: var(--accent-color);
            left: 15%;
            top: 30%;
            animation-delay: 2s;
        }
        
        .balloon:nth-child(3)::before {
            background-color: var(--accent-color);
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-20px);
            }
        }
        
        @media (max-width: 768px) {
            .invitation-title {
                font-size: 2rem;
            }
            
            .invitation-subtitle {
                font-size: 1.2rem;
            }
            
            .birthday-person {
                font-size: 1.5rem;
            }
            
            .invitation-message {
                font-size: 1rem;
            }
            
            .detail-item {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="invitation-container">
        <div class="confetti">
            <div class="confetti-piece"></div>
            <div class="confetti-piece"></div>
            <div class="confetti-piece"></div>
            <div class="confetti-piece"></div>
            <div class="confetti-piece"></div>
            <div class="confetti-piece"></div>
            <div class="confetti-piece"></div>
            <div class="confetti-piece"></div>
            <div class="confetti-piece"></div>
            <div class="confetti-piece"></div>
        </div>
        
        <div class="balloon" style="left: 10%; top: 10%;"></div>
        <div class="balloon"></div>
        <div class="balloon"></div>
        
        <div class="invitation-header">
            <h1 class="invitation-title">دعوة عيد ميلاد</h1>
            <p class="invitation-subtitle">يسرنا دعوتكم لحضور حفل عيد ميلاد</p>
            <div class="birthday-person">سارة</div>
        </div>
        
        <div class="invitation-body">
            <p class="invitation-message">
                نتشرف بدعوتكم لحضور حفل عيد ميلاد سارة<br>
                لنحتفل معاً بمناسبة عيد ميلادها العاشر<br>
                سنقضي وقتاً ممتعاً مليئاً بالمرح والألعاب والمفاجآت
            </p>
            
            <div class="invitation-details">
                <div class="detail-item">
                    <div class="detail-icon">📅</div>
                    <h3 class="detail-title">التاريخ</h3>
                    <p class="detail-text">20/06/2025</p>
                </div>
                
                <div class="detail-item">
                    <div class="detail-icon">🕒</div>
                    <h3 class="detail-title">الوقت</h3>
                    <p class="detail-text">4:00 مساءً</p>
                </div>
                
                <div class="detail-item">
                    <div class="detail-icon">📍</div>
                    <h3 class="detail-title">المكان</h3>
                    <p class="detail-text">قاعة الأطفال، الرياض</p>
                </div>
            </div>
            
            <!-- هنا يمكن إضافة QR Code أو الباركود -->
            <div id="qr-code-placeholder" style="margin: 20px auto; width: 150px; height: 150px; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center;">
                QR Code
            </div>
        </div>
        
        <div class="invitation-footer">
            <p>نتطلع لحضوركم ومشاركتنا فرحتنا</p>
            <a href="#" class="rsvp-button">تأكيد الحضور</a>
        </div>
    </div>
</body>
</html>
