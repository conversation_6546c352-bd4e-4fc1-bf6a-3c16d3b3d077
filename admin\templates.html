<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قوالب التصميم | دعوات إلكترونية</title>
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/responsive.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Tajawal is a good Arabic font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/admin.css">
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <div class="admin-logo">
                    <div class="text-logo">دعوات إلكترونية</div>
                </div>
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="صورة المستخدم">
                </div>
                <div class="user-info">
                    <h3>أحمد محمد</h3>
                    <p>مدير النظام</p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="index.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>المستخدمين</span>
                        </a>
                    </li>
                    <li>
                        <a href="invitations.html">
                            <i class="fas fa-envelope-open-text"></i>
                            <span>الدعوات</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="templates.html">
                            <i class="fas fa-paint-brush"></i>
                            <span>قوالب التصميم</span>
                        </a>
                    </li>
                    <li>
                        <a href="packages.html">
                            <i class="fas fa-box"></i>
                            <span>الباقات</span>
                        </a>
                    </li>
                    <li>
                        <a href="payments.html">
                            <i class="fas fa-credit-card"></i>
                            <span>المدفوعات</span>
                        </a>
                    </li>
                    <li>
                        <a href="reports.html">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <a href="login.html" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="بحث...">
                </div>

                <div class="header-actions">
                    <div class="header-notification">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="header-message">
                        <i class="fas fa-envelope"></i>
                        <span class="message-badge">5</span>
                    </div>
                    <div class="header-profile">
                        <img src="images/admin-avatar.png" alt="صورة المستخدم">
                    </div>
                </div>
            </header>

            <div class="admin-content">
                <div class="page-header">
                    <h1>قوالب التصميم</h1>
                    <a href="template-editor.html" class="btn btn-primary add-new-btn">
                        <i class="fas fa-plus"></i>
                        إضافة قالب جديد
                    </a>
                </div>

                <!-- Templates Filters -->
                <div class="filters-container">
                    <div class="filters-wrapper">
                        <div class="filter-group">
                            <label for="category-filter">التصنيف</label>
                            <select id="category-filter">
                                <option value="all">الكل</option>
                                <option value="wedding">زفاف</option>
                                <option value="birthday">عيد ميلاد</option>
                                <option value="graduation">تخرج</option>
                                <option value="conference">مؤتمر</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="style-filter">النمط</label>
                            <select id="style-filter">
                                <option value="all">الكل</option>
                                <option value="modern">عصري</option>
                                <option value="classic">كلاسيكي</option>
                                <option value="elegant">أنيق</option>
                                <option value="simple">بسيط</option>
                                <option value="colorful">ملون</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="status-filter">الحالة</label>
                            <select id="status-filter">
                                <option value="all">الكل</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>

                    <div class="search-wrapper">
                        <input type="text" placeholder="بحث عن قالب...">
                        <button class="btn btn-primary">بحث</button>
                    </div>
                </div>

                <!-- Templates Grid -->
                <div class="templates-grid">
                    <!-- Template Card 1 -->
                    <div class="template-card">
                        <div class="template-image">
                            <img src="images/template1.jpg" alt="قالب زفاف أنيق">
                            <div class="template-overlay">
                                <button class="btn btn-primary preview-btn">معاينة</button>
                                <a href="template-editor.html" class="btn btn-secondary edit-btn">تعديل</a>
                            </div>
                        </div>
                        <div class="template-info">
                            <h3>قالب زفاف أنيق</h3>
                            <div class="template-meta">
                                <span class="template-category">زفاف</span>
                                <span class="template-style">أنيق</span>
                            </div>
                            <div class="template-stats">
                                <div class="stat">
                                    <i class="fas fa-eye"></i>
                                    <span>1,250</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-download"></i>
                                    <span>450</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-star"></i>
                                    <span>4.8</span>
                                </div>
                            </div>
                            <div class="template-actions">
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider round"></span>
                                </label>
                                <button class="action-btn delete-btn" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Template Card 2 -->
                    <div class="template-card">
                        <div class="template-image">
                            <img src="images/template2.jpg" alt="قالب عيد ميلاد ملون">
                            <div class="template-overlay">
                                <button class="btn btn-primary preview-btn">معاينة</button>
                                <a href="template-editor.html" class="btn btn-secondary edit-btn">تعديل</a>
                            </div>
                        </div>
                        <div class="template-info">
                            <h3>قالب عيد ميلاد ملون</h3>
                            <div class="template-meta">
                                <span class="template-category">عيد ميلاد</span>
                                <span class="template-style">ملون</span>
                            </div>
                            <div class="template-stats">
                                <div class="stat">
                                    <i class="fas fa-eye"></i>
                                    <span>980</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-download"></i>
                                    <span>320</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-star"></i>
                                    <span>4.5</span>
                                </div>
                            </div>
                            <div class="template-actions">
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider round"></span>
                                </label>
                                <button class="action-btn delete-btn" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Template Card 3 -->
                    <div class="template-card">
                        <div class="template-image">
                            <img src="images/template3.jpg" alt="قالب تخرج عصري">
                            <div class="template-overlay">
                                <button class="btn btn-primary preview-btn">معاينة</button>
                                <a href="template-editor.html" class="btn btn-secondary edit-btn">تعديل</a>
                            </div>
                        </div>
                        <div class="template-info">
                            <h3>قالب تخرج عصري</h3>
                            <div class="template-meta">
                                <span class="template-category">تخرج</span>
                                <span class="template-style">عصري</span>
                            </div>
                            <div class="template-stats">
                                <div class="stat">
                                    <i class="fas fa-eye"></i>
                                    <span>750</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-download"></i>
                                    <span>210</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-star"></i>
                                    <span>4.2</span>
                                </div>
                            </div>
                            <div class="template-actions">
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider round"></span>
                                </label>
                                <button class="action-btn delete-btn" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Template Card 4 -->
                    <div class="template-card">
                        <div class="template-image">
                            <img src="images/template4.jpg" alt="قالب مؤتمر بسيط">
                            <div class="template-overlay">
                                <button class="btn btn-primary preview-btn">معاينة</button>
                                <a href="template-editor.html" class="btn btn-secondary edit-btn">تعديل</a>
                            </div>
                        </div>
                        <div class="template-info">
                            <h3>قالب مؤتمر بسيط</h3>
                            <div class="template-meta">
                                <span class="template-category">مؤتمر</span>
                                <span class="template-style">بسيط</span>
                            </div>
                            <div class="template-stats">
                                <div class="stat">
                                    <i class="fas fa-eye"></i>
                                    <span>620</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-download"></i>
                                    <span>180</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-star"></i>
                                    <span>4.0</span>
                                </div>
                            </div>
                            <div class="template-actions">
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider round"></span>
                                </label>
                                <button class="action-btn delete-btn" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Template Card 5 -->
                    <div class="template-card">
                        <div class="template-image">
                            <img src="images/template5.jpg" alt="قالب زفاف كلاسيكي">
                            <div class="template-overlay">
                                <button class="btn btn-primary preview-btn">معاينة</button>
                                <a href="template-editor.html" class="btn btn-secondary edit-btn">تعديل</a>
                            </div>
                        </div>
                        <div class="template-info">
                            <h3>قالب زفاف كلاسيكي</h3>
                            <div class="template-meta">
                                <span class="template-category">زفاف</span>
                                <span class="template-style">كلاسيكي</span>
                            </div>
                            <div class="template-stats">
                                <div class="stat">
                                    <i class="fas fa-eye"></i>
                                    <span>890</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-download"></i>
                                    <span>300</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-star"></i>
                                    <span>4.6</span>
                                </div>
                            </div>
                            <div class="template-actions">
                                <label class="switch">
                                    <input type="checkbox">
                                    <span class="slider round"></span>
                                </label>
                                <button class="action-btn delete-btn" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Template Card 6 -->
                    <div class="template-card">
                        <div class="template-image">
                            <img src="images/template6.jpg" alt="قالب معرض أنيق">
                            <div class="template-overlay">
                                <button class="btn btn-primary preview-btn">معاينة</button>
                                <a href="template-editor.html" class="btn btn-secondary edit-btn">تعديل</a>
                            </div>
                        </div>
                        <div class="template-info">
                            <h3>قالب معرض أنيق</h3>
                            <div class="template-meta">
                                <span class="template-category">أخرى</span>
                                <span class="template-style">أنيق</span>
                            </div>
                            <div class="template-stats">
                                <div class="stat">
                                    <i class="fas fa-eye"></i>
                                    <span>520</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-download"></i>
                                    <span>150</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-star"></i>
                                    <span>4.3</span>
                                </div>
                            </div>
                            <div class="template-actions">
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider round"></span>
                                </label>
                                <button class="action-btn delete-btn" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination">
                    <div class="pagination-info">
                        <p>عرض 1-6 من 24 قالب</p>
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn">4</button>
                        <button class="pagination-btn">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                    <div class="pagination-options">
                        <label for="rows-per-page">عرض</label>
                        <select id="rows-per-page">
                            <option value="6">6</option>
                            <option value="12">12</option>
                            <option value="18">18</option>
                            <option value="24">24</option>
                        </select>
                        <span>لكل صفحة</span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Template Preview Modal -->
    <div class="modal" id="preview-modal">
        <div class="modal-content preview-modal-content">
            <div class="modal-header">
                <h2>معاينة القالب</h2>
                <button class="close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="template-preview">
                    <img src="images/template1.jpg" alt="معاينة القالب" id="preview-image">
                </div>
            </div>
        </div>
    </div>

    <script src="../js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle sidebar
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            const adminContainer = document.querySelector('.admin-container');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    adminContainer.classList.toggle('sidebar-collapsed');
                });
            }

            // Template preview functionality
            const previewButtons = document.querySelectorAll('.preview-btn');
            const previewModal = document.getElementById('preview-modal');
            const closeModal = document.querySelector('.close-modal');
            const previewImage = document.getElementById('preview-image');

            previewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const templateCard = this.closest('.template-card');
                    const templateImage = templateCard.querySelector('.template-image img');
                    const templateTitle = templateCard.querySelector('h3').textContent;

                    previewImage.src = templateImage.src;
                    previewImage.alt = templateTitle;
                    document.querySelector('.modal-header h2').textContent = 'معاينة: ' + templateTitle;

                    previewModal.classList.add('show');
                });
            });

            if (closeModal) {
                closeModal.addEventListener('click', function() {
                    previewModal.classList.remove('show');
                });
            }

            // Delete template functionality
            const deleteButtons = document.querySelectorAll('.delete-btn');

            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (confirm('هل أنت متأكد من حذف هذا القالب؟')) {
                        // Delete template logic would go here
                        const templateCard = this.closest('.template-card');
                        templateCard.remove();
                    }
                });
            });
        });
    </script>
    <style>
        /* Additional styles for templates page */
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .template-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .template-card:hover {
            transform: translateY(-5px);
        }

        .template-image {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        .template-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .template-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .template-image:hover .template-overlay {
            opacity: 1;
        }

        .template-info {
            padding: 20px;
        }

        .template-info h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .template-meta {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .template-category,
        .template-style {
            background-color: var(--admin-gray-light);
            color: var(--admin-gray-dark);
            padding: 3px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
        }

        .template-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .stat {
            display: flex;
            align-items: center;
            gap: 5px;
            color: var(--admin-gray);
            font-size: 0.9rem;
        }

        .template-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* Toggle Switch */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: var(--admin-primary);
        }

        input:focus + .slider {
            box-shadow: 0 0 1px var(--admin-primary);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .slider.round {
            border-radius: 34px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        /* Preview Modal */
        .preview-modal-content {
            max-width: 800px;
            width: 90%;
        }

        .template-preview {
            width: 100%;
            text-align: center;
        }

        .template-preview img {
            max-width: 100%;
            max-height: 70vh;
            border-radius: 5px;
        }
    </style>
</body>
</html>
