<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استعادة كلمة المرور | دعوات إلكترونية</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Tajawal is a good Arabic font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/auth.css">
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="logo">
                    <a href="index.html">
                        <div class="text-logo">دعوات إلكترونية</div>
                    </a>
                </div>
                <div class="nav-links">
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="index.html#features">المميزات</a></li>
                        <li><a href="index.html#how-it-works">كيف يعمل</a></li>
                        <li><a href="index.html#testimonials">العملاء</a></li>
                        <li><a href="index.html#pricing">الباقات والأسعار</a></li>
                        <li><a href="index.html#contact">تواصل معنا</a></li>
                    </ul>
                </div>
                <div class="auth-buttons">
                    <a href="login.html" class="btn btn-login">تسجيل الدخول</a>
                    <a href="admin/login.html" class="btn btn-secondary">لوحة التحكم</a>
                </div>
                <div class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Forgot Password Section -->
    <section class="auth-section">
        <div class="container">
            <div class="auth-container forgot-password-container">
                <div class="auth-form-container">
                    <h2>استعادة كلمة المرور</h2>
                    <p>أدخل بريدك الإلكتروني لإرسال رابط إعادة تعيين كلمة المرور</p>
                    
                    <form class="auth-form" id="forgot-password-form">
                        <div class="form-group">
                            <label for="email">البريد الإلكتروني</label>
                            <div class="input-with-icon">
                                <i class="fas fa-envelope"></i>
                                <input type="email" id="email" name="email" placeholder="أدخل بريدك الإلكتروني" required>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block">إرسال رابط إعادة التعيين</button>
                    </form>
                    
                    <div class="auth-footer">
                        <p>تذكرت كلمة المرور؟ <a href="login.html">تسجيل الدخول</a></p>
                    </div>
                </div>
                
                <div class="auth-image">
                    <div class="auth-image-content">
                        <h3>استعادة كلمة المرور</h3>
                        <p>لا تقلق، سنساعدك في استعادة الوصول إلى حسابك. سنرسل لك رابطاً لإعادة تعيين كلمة المرور عبر البريد الإلكتروني.</p>
                        <div class="auth-steps">
                            <div class="auth-step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h4>أدخل بريدك الإلكتروني</h4>
                                    <p>أدخل البريد الإلكتروني المرتبط بحسابك</p>
                                </div>
                            </div>
                            <div class="auth-step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h4>تحقق من بريدك</h4>
                                    <p>افتح الرابط المرسل إلى بريدك الإلكتروني</p>
                                </div>
                            </div>
                            <div class="auth-step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h4>أنشئ كلمة مرور جديدة</h4>
                                    <p>أدخل كلمة مرور جديدة وقم بتأكيدها</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Success Message (Hidden by default) -->
            <div class="auth-success-message" id="success-message" style="display: none;">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2>تم إرسال رابط إعادة التعيين</h2>
                <p>لقد أرسلنا رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. يرجى التحقق من صندوق الوارد الخاص بك واتباع التعليمات لإعادة تعيين كلمة المرور.</p>
                <p class="note">إذا لم تجد الرسالة، يرجى التحقق من مجلد البريد غير المرغوب فيه (Spam).</p>
                <div class="success-actions">
                    <a href="login.html" class="btn btn-secondary">العودة إلى تسجيل الدخول</a>
                    <button class="btn btn-primary" id="resend-link">إعادة إرسال الرابط</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <div class="text-logo">دعوات إلكترونية</div>
                    <p>شركة للاتصالات وتقنية المعلومات</p>
                    <p>الرياض - المملكة العربية السعودية</p>
                </div>
                <div class="footer-links">
                    <div class="footer-links-column">
                        <h3>روابط سريعة</h3>
                        <ul>
                            <li><a href="index.html">الرئيسية</a></li>
                            <li><a href="index.html#features">المميزات</a></li>
                            <li><a href="index.html#how-it-works">كيف يعمل</a></li>
                            <li><a href="index.html#testimonials">العملاء</a></li>
                            <li><a href="index.html#pricing">الباقات والأسعار</a></li>
                            <li><a href="index.html#contact">تواصل معنا</a></li>
                        </ul>
                    </div>
                    <div class="footer-links-column">
                        <h3>الشروط والأحكام</h3>
                        <ul>
                            <li><a href="terms.html">شروط الاستخدام</a></li>
                            <li><a href="privacy.html">سياسة الخصوصية</a></li>
                            <li><a href="faq.html">الأسئلة الشائعة</a></li>
                        </ul>
                    </div>
                </div>
                <div class="footer-social">
                    <h3>تواصل معنا</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-whatsapp"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© 2025 جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const forgotPasswordForm = document.getElementById('forgot-password-form');
            const authContainer = document.querySelector('.auth-container');
            const successMessage = document.getElementById('success-message');
            const resendLink = document.getElementById('resend-link');
            
            if (forgotPasswordForm) {
                forgotPasswordForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // Get email value
                    const email = document.getElementById('email').value;
                    
                    // Simulate API call to send reset link
                    // In a real application, this would be an AJAX call to your backend
                    
                    // Show success message
                    authContainer.style.display = 'none';
                    successMessage.style.display = 'block';
                });
            }
            
            if (resendLink) {
                resendLink.addEventListener('click', function() {
                    alert('تم إعادة إرسال الرابط بنجاح!');
                });
            }
        });
    </script>
    <style>
        /* Additional styles for forgot password page */
        .forgot-password-container {
            max-width: 900px;
        }
        
        .auth-steps {
            margin-top: 30px;
        }
        
        .auth-step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 15px;
            flex-shrink: 0;
        }
        
        .step-content h4 {
            font-size: 1.1rem;
            margin-bottom: 5px;
        }
        
        .step-content p {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .auth-success-message {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .success-icon {
            font-size: 4rem;
            color: #4cd964;
            margin-bottom: 20px;
        }
        
        .auth-success-message h2 {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .auth-success-message p {
            color: #666;
            margin-bottom: 20px;
        }
        
        .auth-success-message .note {
            font-size: 0.9rem;
            color: #999;
            margin-bottom: 30px;
        }
        
        .success-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
    </style>
</body>
</html>
