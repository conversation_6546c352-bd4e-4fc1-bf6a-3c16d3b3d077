/**
 * QR Code Generation and Customization Module
 * This module handles QR code generation and positioning on invitations
 */

// QR Code Generator using qrcode.js library
const QRCodeGenerator = {
    /**
     * Generate a QR code and append it to a container
     * @param {string} data - The data to encode in the QR code
     * @param {HTMLElement} container - The container to append the QR code to
     * @param {Object} options - Options for the QR code
     */
    generate: function(data, container, options = {}) {
        // Default options
        const defaultOptions = {
            width: 128,
            height: 128,
            colorDark: "#000000",
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.H
        };
        
        // Merge default options with provided options
        const qrOptions = { ...defaultOptions, ...options };
        
        // Clear the container
        container.innerHTML = '';
        
        // Generate the QR code
        new QRCode(container, {
            text: data,
            width: qrOptions.width,
            height: qrOptions.height,
            colorDark: qrOptions.colorDark,
            colorLight: qrOptions.colorLight,
            correctLevel: qrOptions.correctLevel
        });
        
        return container;
    },
    
    /**
     * Position a QR code on an invitation template
     * @param {HTMLElement} qrCodeElement - The QR code element
     * @param {Object} position - The position object with x, y coordinates
     * @param {HTMLElement} templateContainer - The template container
     */
    positionOnTemplate: function(qrCodeElement, position, templateContainer) {
        // Set the position of the QR code
        qrCodeElement.style.position = 'absolute';
        qrCodeElement.style.top = `${position.y}px`;
        qrCodeElement.style.left = `${position.x}px`;
        
        // Make the QR code draggable
        this.makeDraggable(qrCodeElement, templateContainer);
    },
    
    /**
     * Make an element draggable within a container
     * @param {HTMLElement} element - The element to make draggable
     * @param {HTMLElement} container - The container to constrain dragging to
     */
    makeDraggable: function(element, container) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
        
        // Add a draggable handle
        const handle = document.createElement('div');
        handle.className = 'qr-drag-handle';
        handle.innerHTML = '<i class="fas fa-arrows-alt"></i>';
        element.appendChild(handle);
        
        // Mouse down event on the handle
        handle.onmousedown = dragMouseDown;
        
        function dragMouseDown(e) {
            e = e || window.event;
            e.preventDefault();
            // Get the mouse cursor position at startup
            pos3 = e.clientX;
            pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            // Call a function whenever the cursor moves
            document.onmousemove = elementDrag;
        }
        
        function elementDrag(e) {
            e = e || window.event;
            e.preventDefault();
            // Calculate the new cursor position
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            
            // Get container boundaries
            const containerRect = container.getBoundingClientRect();
            const elementRect = element.getBoundingClientRect();
            
            // Calculate new position with boundaries
            let newTop = element.offsetTop - pos2;
            let newLeft = element.offsetLeft - pos1;
            
            // Apply boundaries
            if (newTop < 0) newTop = 0;
            if (newLeft < 0) newLeft = 0;
            if (newTop + elementRect.height > containerRect.height) {
                newTop = containerRect.height - elementRect.height;
            }
            if (newLeft + elementRect.width > containerRect.width) {
                newLeft = containerRect.width - elementRect.width;
            }
            
            // Set the element's new position
            element.style.top = newTop + "px";
            element.style.left = newLeft + "px";
            
            // Update the position data attribute for saving
            element.setAttribute('data-position-x', newLeft);
            element.setAttribute('data-position-y', newTop);
        }
        
        function closeDragElement() {
            // Stop moving when mouse button is released
            document.onmouseup = null;
            document.onmousemove = null;
        }
    },
    
    /**
     * Get the current position of a QR code
     * @param {HTMLElement} qrCodeElement - The QR code element
     * @returns {Object} - The position object with x, y coordinates
     */
    getPosition: function(qrCodeElement) {
        return {
            x: parseInt(qrCodeElement.getAttribute('data-position-x') || 0),
            y: parseInt(qrCodeElement.getAttribute('data-position-y') || 0)
        };
    }
};

// Export the QR code generator
window.QRCodeGenerator = QRCodeGenerator;
