/* Authentication Pages Styles (<PERSON><PERSON>, Register) */

.auth-section {
    padding: 150px 0 100px;
}

.auth-container {
    display: flex;
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: var(--shadow);
    overflow: hidden;
    max-width: 1000px;
    margin: 0 auto;
}

.auth-form-container {
    flex: 1;
    padding: 50px;
}

.auth-form-container h2 {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.auth-form-container p {
    color: var(--light-text);
    margin-bottom: 30px;
}

.auth-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--lighter-text);
}

.input-with-icon input,
.input-with-icon select {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    font-size: 1rem;
    transition: var(--transition);
}

.input-with-icon input:focus,
.input-with-icon select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(108, 99, 255, 0.2);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-left: 8px;
}

.forgot-password {
    color: var(--primary-color);
    font-size: 0.9rem;
}

.forgot-password:hover {
    text-decoration: underline;
}

.btn-block {
    width: 100%;
    margin-bottom: 20px;
}

.auth-divider {
    position: relative;
    text-align: center;
    margin: 30px 0;
}

.auth-divider::before {
    content: "";
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    height: 1px;
    background-color: var(--border-color);
    z-index: 1;
}

.auth-divider span {
    position: relative;
    background-color: var(--white);
    padding: 0 15px;
    z-index: 2;
    color: var(--light-text);
}

.social-login {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

.btn-social {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px;
    border-radius: 5px;
    font-weight: 500;
    transition: var(--transition);
}

.btn-google {
    background-color: #fff;
    color: #333;
    border: 1px solid #ddd;
}

.btn-google:hover {
    background-color: #f1f1f1;
}

.btn-facebook {
    background-color: #3b5998;
    color: #fff;
}

.btn-facebook:hover {
    background-color: #2d4373;
}

.auth-footer {
    text-align: center;
    color: var(--light-text);
}

.auth-footer a {
    color: var(--primary-color);
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

.auth-image {
    flex: 1;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 50px;
    position: relative;
    overflow: hidden;
}

.auth-image::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0) 100%);
}

.auth-image-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.auth-image-content h3 {
    font-size: 2rem;
    margin-bottom: 20px;
}

.auth-image-content p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.auth-features {
    text-align: right;
}

.auth-feature {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.auth-feature i {
    margin-left: 10px;
    font-size: 1.2rem;
    color: #4cd964;
}

/* Checkbox Styles */
.checkbox-group {
    display: flex;
    align-items: flex-start;
}

.checkbox-group input {
    margin-left: 10px;
    margin-top: 3px;
}

.checkbox-group label {
    font-size: 0.9rem;
    color: var(--light-text);
}

.checkbox-group a {
    color: var(--primary-color);
}

.checkbox-group a:hover {
    text-decoration: underline;
}

/* Responsive Styles */
@media screen and (max-width: 992px) {
    .auth-container {
        flex-direction: column;
    }
    
    .auth-image {
        display: none;
    }
}

@media screen and (max-width: 768px) {
    .auth-section {
        padding: 120px 0 60px;
    }
    
    .auth-form-container {
        padding: 30px 20px;
    }
    
    .auth-form-container h2 {
        font-size: 1.8rem;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
}
