<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دعوة زفاف أنيقة</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap');
        
        :root {
            --primary-color: #6c63ff;
            --secondary-color: #ff6b6b;
            --accent-color: #ffd166;
            --text-color: #333;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: var(--light-color);
            color: var(--text-color);
            line-height: 1.6;
        }
        
        .invitation-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .invitation-header {
            background-color: var(--primary-color);
            color: white;
            padding: 40px 20px;
            text-align: center;
            position: relative;
        }
        
        .invitation-header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 0;
            right: 0;
            height: 40px;
            background-color: white;
            border-radius: 50% 50% 0 0;
        }
        
        .invitation-title {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 800;
        }
        
        .invitation-subtitle {
            font-size: 1.5rem;
            font-weight: 300;
            margin-bottom: 20px;
        }
        
        .couple-names {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--accent-color);
        }
        
        .invitation-body {
            padding: 40px 20px;
            text-align: center;
        }
        
        .invitation-message {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.8;
        }
        
        .invitation-details {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .detail-item {
            flex: 1;
            min-width: 200px;
            padding: 20px;
            background-color: var(--light-color);
            border-radius: 10px;
            text-align: center;
        }
        
        .detail-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .detail-title {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .detail-text {
            font-size: 1rem;
        }
        
        .invitation-footer {
            background-color: var(--primary-color);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }
        
        .invitation-footer::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 0;
            right: 0;
            height: 40px;
            background-color: var(--primary-color);
            border-radius: 0 0 50% 50%;
        }
        
        .rsvp-button {
            display: inline-block;
            padding: 12px 30px;
            background-color: var(--accent-color);
            color: var(--dark-color);
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            margin-top: 20px;
            transition: all 0.3s;
        }
        
        .rsvp-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        @media (max-width: 768px) {
            .invitation-title {
                font-size: 2rem;
            }
            
            .invitation-subtitle {
                font-size: 1.2rem;
            }
            
            .couple-names {
                font-size: 1.5rem;
            }
            
            .invitation-message {
                font-size: 1rem;
            }
            
            .detail-item {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="invitation-container">
        <div class="invitation-header">
            <h1 class="invitation-title">دعوة زفاف</h1>
            <p class="invitation-subtitle">يسرنا دعوتكم لحضور حفل زفاف</p>
            <div class="couple-names">أحمد & نورة</div>
        </div>
        
        <div class="invitation-body">
            <p class="invitation-message">
                بسم الله الرحمن الرحيم<br>
                يسرنا أن ندعوكم لمشاركتنا فرحة زفافنا<br>
                وذلك بحضوركم حفل الزفاف الذي سيقام بإذن الله تعالى
            </p>
            
            <div class="invitation-details">
                <div class="detail-item">
                    <div class="detail-icon">📅</div>
                    <h3 class="detail-title">التاريخ</h3>
                    <p class="detail-text">15/05/2025</p>
                </div>
                
                <div class="detail-item">
                    <div class="detail-icon">🕒</div>
                    <h3 class="detail-title">الوقت</h3>
                    <p class="detail-text">7:00 مساءً</p>
                </div>
                
                <div class="detail-item">
                    <div class="detail-icon">📍</div>
                    <h3 class="detail-title">المكان</h3>
                    <p class="detail-text">قاعة الأميرات، الرياض</p>
                </div>
            </div>
            
            <!-- هنا يمكن إضافة QR Code أو الباركود -->
            <div id="qr-code-placeholder" style="margin: 20px auto; width: 150px; height: 150px; background-color: #f0f0f0; display: flex; align-items: center; justify-content: center;">
                QR Code
            </div>
        </div>
        
        <div class="invitation-footer">
            <p>نتطلع لحضوركم ومشاركتنا فرحتنا</p>
            <a href="#" class="rsvp-button">تأكيد الحضور</a>
        </div>
    </div>
</body>
</html>
