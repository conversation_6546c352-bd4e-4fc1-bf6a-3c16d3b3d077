/* Main CSS File */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6c63ff;
    --secondary-color: #4a42e8;
    --accent-color: #ff6b6b;
    --text-color: #333;
    --light-text: #666;
    --lighter-text: #999;
    --white: #fff;
    --light-bg: #f9f9f9;
    --border-color: #eee;
    --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Tajawal', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--white);
    direction: rtl;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-header h2 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.section-header p {
    font-size: 1.2rem;
    color: var(--light-text);
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-3px);
}

.btn-login {
    background-color: var(--white);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    padding: 8px 20px;
}

.btn-login:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* Navbar Styles */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: var(--white);
    box-shadow: var(--shadow);
    z-index: 1000;
    padding: 15px 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.text-logo {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    padding: 5px 0;
}

.nav-links ul {
    display: flex;
}

.nav-links li {
    margin-left: 30px;
}

.nav-links a {
    font-weight: 500;
    transition: var(--transition);
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--primary-color);
}

.menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Placeholder Image Styles */
.placeholder-image {
    width: 300px;
    height: 500px;
    background-color: #f0f0f0;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    padding: 20px;
    box-shadow: var(--shadow);
}

.placeholder-icon {
    font-size: 80px;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.placeholder-text {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    text-align: center;
}

.how-it-works-image .placeholder-image {
    height: 400px;
}

.how-it-works-image .placeholder-icon {
    font-size: 60px;
}

.how-it-works-image .placeholder-text {
    font-size: 20px;
}

/* Hero Section Styles */
.hero {
    padding: 150px 0 100px;
    background-color: var(--light-bg);
}

.hero-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.hero-text {
    flex: 1;
    padding-left: 50px;
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: var(--light-text);
}

.hero-buttons {
    margin-bottom: 20px;
}

.hero-buttons .btn {
    margin-left: 15px;
    margin-bottom: 10px;
}

.app-buttons {
    display: flex;
    gap: 15px;
}

.app-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 20px;
    background-color: var(--text-color);
    color: var(--white);
    border-radius: 5px;
    transition: var(--transition);
}

.app-button:hover {
    background-color: var(--primary-color);
}

/* Features Section Styles */
.features {
    padding: 100px 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature-card {
    background-color: var(--white);
    border-radius: 10px;
    padding: 30px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-10px);
}

.feature-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.feature-card p {
    color: var(--light-text);
}

/* How It Works Section Styles */
.how-it-works {
    padding: 100px 0;
    background-color: var(--light-bg);
}

.how-it-works-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.how-it-works-image {
    flex: 1;
    text-align: center;
}

.how-it-works-steps {
    flex: 1;
}

.step {
    display: flex;
    margin-bottom: 30px;
}

.step-number {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin-left: 20px;
    flex-shrink: 0;
}

.step-content h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.step-content p {
    color: var(--light-text);
}

/* Testimonials Section Styles */
.testimonials {
    padding: 100px 0;
}

.testimonials-slider {
    display: flex;
    gap: 30px;
    overflow-x: auto;
    padding: 20px 0;
    scrollbar-width: none; /* Firefox */
}

.testimonials-slider::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
}

.testimonial-card {
    background-color: var(--white);
    border-radius: 10px;
    padding: 30px;
    box-shadow: var(--shadow);
    min-width: 300px;
    flex: 1;
}

.testimonial-content h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.testimonial-content p {
    color: var(--light-text);
    margin-bottom: 20px;
}

.testimonial-author p {
    font-weight: 600;
    color: var(--text-color);
}

/* Pricing Section Styles */
.pricing {
    padding: 100px 0;
    background-color: var(--light-bg);
}

.pricing-cards {
    display: flex;
    gap: 30px;
    justify-content: center;
    flex-wrap: wrap;
}

.pricing-card {
    background-color: var(--white);
    border-radius: 10px;
    padding: 40px 30px;
    box-shadow: var(--shadow);
    flex: 1;
    min-width: 300px;
    max-width: 350px;
    transition: var(--transition);
}

.pricing-card.featured {
    transform: scale(1.05);
    border: 2px solid var(--primary-color);
}

.pricing-card:hover {
    transform: translateY(-10px);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-header {
    text-align: center;
    margin-bottom: 30px;
}

.pricing-header h3 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.price {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-color);
}

.pricing-features {
    margin-bottom: 30px;
}

.pricing-features ul li {
    margin-bottom: 15px;
    position: relative;
    padding-right: 25px;
}

.pricing-features ul li::before {
    content: "✓";
    color: var(--primary-color);
    position: absolute;
    right: 0;
}

.pricing-footer {
    text-align: center;
}

/* Business Solutions Section Styles */
.business-solutions {
    padding: 80px 0;
    background-color: var(--primary-color);
    color: var(--white);
}

.business-solutions-content {
    text-align: center;
}

.business-solutions h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.business-solutions p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.business-solutions .btn-primary {
    background-color: var(--white);
    color: var(--primary-color);
}

.business-solutions .btn-primary:hover {
    background-color: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

/* Contact Section Styles */
.contact {
    padding: 100px 0;
}

.contact-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 30px;
}

.contact-info {
    flex: 1;
    min-width: 300px;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.contact-icon {
    width: 60px;
    height: 60px;
    background-color: var(--light-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-left: 20px;
}

.contact-text h3 {
    font-size: 1.3rem;
    margin-bottom: 5px;
}

.contact-buttons {
    flex: 1;
    text-align: center;
    min-width: 300px;
}

/* Footer Styles */
.footer {
    background-color: var(--text-color);
    color: var(--white);
    padding: 80px 0 20px;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 40px;
    margin-bottom: 50px;
}

.footer-logo {
    flex: 1;
    min-width: 250px;
}

.footer-logo .text-logo {
    font-size: 28px;
    margin-bottom: 20px;
    color: var(--white);
}

.footer-links {
    flex: 2;
    display: flex;
    justify-content: space-around;
}

.footer-links-column h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.footer-links-column ul li {
    margin-bottom: 10px;
}

.footer-links-column ul li a {
    color: var(--white);
    opacity: 0.8;
    transition: var(--transition);
}

.footer-links-column ul li a:hover {
    opacity: 1;
    color: var(--primary-color);
}

.footer-social {
    flex: 1;
    min-width: 250px;
}

.footer-social h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: var(--transition);
}

.social-icons a:hover {
    background-color: var(--primary-color);
    transform: translateY(-5px);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    opacity: 0.7;
}
