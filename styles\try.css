/* Try Free Page Styles */

.try-section {
    padding: 150px 0 100px;
}

.try-container {
    max-width: 1000px;
    margin: 0 auto;
}

.try-header {
    text-align: center;
    margin-bottom: 50px;
}

.try-header h1 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.try-header p {
    font-size: 1.2rem;
    color: var(--light-text);
}

.try-content {
    display: flex;
    gap: 40px;
    margin-bottom: 80px;
}

.try-form-container {
    flex: 1;
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: var(--shadow);
    padding: 40px;
}

.try-form-header {
    margin-bottom: 30px;
}

.try-form-header h2 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.try-form-header p {
    color: var(--light-text);
}

.try-form {
    margin-bottom: 20px;
}

.try-features {
    flex: 1;
    padding: 40px;
    background-color: var(--light-bg);
    border-radius: 10px;
}

.try-features h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 30px;
    text-align: center;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.features-list li {
    display: flex;
    gap: 20px;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background-color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary-color);
    flex-shrink: 0;
}

.feature-content h4 {
    font-size: 1.2rem;
    margin-bottom: 8px;
}

.feature-content p {
    color: var(--light-text);
}

.try-testimonials {
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: var(--shadow);
    padding: 40px;
}

.try-testimonials h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 30px;
    text-align: center;
}

/* Responsive Styles */
@media screen and (max-width: 992px) {
    .try-content {
        flex-direction: column;
    }
}

@media screen and (max-width: 768px) {
    .try-section {
        padding: 120px 0 60px;
    }
    
    .try-header h1 {
        font-size: 2rem;
    }
    
    .try-form-container,
    .try-features,
    .try-testimonials {
        padding: 30px 20px;
    }
    
    .try-form-header h2 {
        font-size: 1.5rem;
    }
    
    .try-features h3,
    .try-testimonials h3 {
        font-size: 1.3rem;
    }
    
    .features-list li {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
}
