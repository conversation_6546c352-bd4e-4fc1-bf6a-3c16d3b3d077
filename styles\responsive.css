/* Responsive CSS File */

/* Large Screens (Desktops) */
@media screen and (max-width: 1200px) {
    .container {
        max-width: 960px;
    }
}

/* Medium Screens (Tablets) */
@media screen and (max-width: 992px) {
    .container {
        max-width: 720px;
    }

    .hero-content {
        flex-direction: column;
    }

    .hero-text {
        padding-left: 0;
        margin-bottom: 50px;
        text-align: center;
    }

    .hero-buttons, .app-buttons {
        justify-content: center;
    }

    .how-it-works-content {
        flex-direction: column;
    }

    .how-it-works-image {
        margin-bottom: 50px;
    }

    .pricing-card {
        max-width: 100%;
    }

    .pricing-card.featured {
        transform: scale(1);
    }

    .pricing-card.featured:hover {
        transform: translateY(-10px);
    }
}

/* Small Screens (Mobile Landscape) */
@media screen and (max-width: 768px) {
    .container {
        max-width: 540px;
    }

    .nav-links, .auth-buttons {
        display: none;
    }

    .menu-toggle {
        display: block;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-slider {
        flex-direction: column;
    }

    .testimonial-card {
        min-width: 100%;
    }

    .pricing-cards {
        flex-direction: column;
        align-items: center;
    }

    .pricing-card {
        width: 100%;
        margin-bottom: 30px;
    }

    .footer-content {
        flex-direction: column;
    }

    .footer-links {
        flex-direction: column;
        gap: 30px;
    }
}

/* Extra Small Screens (Mobile Portrait) */
@media screen and (max-width: 576px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 120px 0 80px;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .section-header {
        margin-bottom: 30px;
    }

    .features, .how-it-works, .testimonials, .pricing, .contact {
        padding: 60px 0;
    }

    .app-buttons {
        flex-direction: column;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
    }

    .contact-icon {
        margin-left: 0;
        margin-bottom: 15px;
    }
}
